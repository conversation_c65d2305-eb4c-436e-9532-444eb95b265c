import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useGlobalSignalR } from '@/composables/useSignalR'
import { createControlMessage } from '@/utils/signalr-config'
import { ElMessage } from 'element-plus'

/**
 * 称重业务逻辑Hook
 * 专注于称重模块的控制逻辑
 */
export function useChengzhongSignalR() {
  const signalR = useGlobalSignalR()

  // 加载状态
  const isLoading = ref(false)

  const commandMap: Record<string, string> = {
    // 读写卡功能
    '内桶开盖工位读写卡-读写卡': 'Manu_内桶开盖工位读写卡',

    // 称重功能
    '內桶开盖工位称重清零-称重清零': 'Manu_內桶开盖工位称重清零',

    // 密码盖控制
    '內桶开盖工位密码盖遥控-开锁': 'Manu_內桶开盖工位密码盖遥控开锁',
    '內桶开盖工位密码盖遥控-闭锁': 'Manu_內桶开盖工位密码盖遥控闭锁',
    '內桶开盖工位密码盖供电-供电': 'Manu_內桶开盖工位密码盖供电',

    // 夹盖气缸控制
    '内桶开盖工位夹盖气缸-缩回': 'Manu_内桶开盖工位夹盖气缸缩回',
    '内桶开盖工位夹盖气缸-伸出': 'Manu_内桶开盖工位夹盖气缸伸出',

    // 压盖气缸控制
    '内桶开盖工位压盖气缸-缩回': 'Manu_内桶开盖工位压盖气缸缩回',
    '内桶开盖工位压盖气缸-伸出': 'Manu_内桶开盖工位压盖气缸伸出',

    // 开盖步骤控制
    '內桶开盖工位开盖步-初始化': 'Manu_內桶开盖工位开盖步初始化',
    '內桶开盖工位开盖步-测试': 'Manu_內桶开盖工位开盖步测试',
    '內桶开盖工位锁盖步-初始化': 'Manu_內桶开盖工位锁盖步初始化',
    '內桶开盖工位锁盖步-测试': 'Manu_內桶开盖工位锁盖步测试'
  }

  // 响应式状态 - 使用any类型避免严格类型检查
  const buttonStates = reactive<any>({
    '内桶开盖工位读写卡': { '读写卡': false },
    '內桶开盖工位称重清零': { '称重清零': false },
    '內桶开盖工位密码盖遥控': { '开锁': false, '闭锁': false },
    '內桶开盖工位密码盖供电': { '供电': false },
    '内桶开盖工位夹盖气缸': { '缩回': false, '伸出': false },
    '内桶开盖工位压盖气缸': { '缩回': false, '伸出': false },
    '內桶开盖工位开盖步': { '初始化': false, '测试': false },
    '內桶开盖工位锁盖步': { '初始化': false, '测试': false }
  })

  // 设备状态指示器 - 初始状态为error（红色）
  const deviceStatus = reactive<any>({
    '内桶开盖工位夹盖气缸': { '缩回到位': 'error', '伸出到位': 'error' },
    '内桶开盖工位压盖气缸': { '缩回到位': 'error', '伸出到位': 'error' }
  })

  /**
   * 发送控制命令
   */
  const sendControlCommand = async (deviceType: string, action: string, params: any = {}): Promise<void> => {
    if (isLoading.value || !signalR.isConnected.value) {
      console.warn('SignalR未连接或正在处理其他命令')
      return
    }

    const commandKey = `${deviceType}-${action}`
    const backendCommand = commandMap[commandKey]

    if (!backendCommand) {
      console.error(`未找到命令映射: ${commandKey}`)
      return
    }

    try {
      isLoading.value = true

      // 维护命令统一调用ExecuteMaintenanceCommand，传入PLC常量作为cmd
      const message = createControlMessage('ExecuteMaintenanceCommand', { cmd: backendCommand })

      // 发送命令 - 通过Hub的HandleCommand方法路由到业务层
      const response = await signalR.send('HandleCommand', JSON.stringify(message))

      // 解析后端返回的JSON响应
      let result: any = {}
      try {
        if (typeof response === 'string') {
          const parsedResponse = JSON.parse(response)
          if (parsedResponse.C) {
            result = JSON.parse(parsedResponse.C)
          }
        }
      } catch (error) {
        console.error('解析响应失败:', error)
        return
      }

      if (result?.Result === 1) {
        ElMessage.success("操作成功")
        // 更新按钮状态
        updateButtonState(deviceType, action)
        console.log(`称重控制命令执行成功: ${deviceType} - ${action}`)
      } else {
        const errorMsg = result?.Msg || '未知错误'
        console.error(`命令执行失败: ${backendCommand}`, errorMsg)

        // 显示错误提示给用户
        ElMessage.error(`操作失败: ${errorMsg}`)
      }

    } catch (error) {
      console.error(`命令发送异常: ${backendCommand}`, error)
      ElMessage.error('命令发送失败')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 更新按钮状态
   */
  const updateButtonState = (deviceType: string, action: string) => {
    if (buttonStates[deviceType]) {
      // 气缸设备实现互斥逻辑
      if (deviceType.includes('气缸')) {
        Object.keys(buttonStates[deviceType]).forEach(key => {
          buttonStates[deviceType][key] = false
        })
        buttonStates[deviceType][action] = true

        // 更新设备状态指示器
        if (deviceStatus[deviceType]) {
          Object.keys(deviceStatus[deviceType]).forEach(key => {
            deviceStatus[deviceType][key] = 'error'
          })
          if (action === '伸出') {
            deviceStatus[deviceType]['伸出到位'] = 'success'
          } else if (action === '缩回') {
            deviceStatus[deviceType]['缩回到位'] = 'success'
          }
        }
      } else {
        // 其他设备的状态切换
        buttonStates[deviceType][action] = !buttonStates[deviceType][action]
      }
    }
  }

  /**
   * 处理实时数据更新
   */
  const handleRealtimeData = (data: any) => {
    try {
      // 解析SignalR消息
      let parsedData = data
      if (typeof data === 'string') {
        parsedData = JSON.parse(data)
      }

      // 处理SignalR消息格式
      if (parsedData && parsedData.type && parsedData.arguments) {
        if (parsedData.arguments.length > 0) {
          const messageContent = parsedData.arguments[0]
          parsedData = typeof messageContent === 'string' ? JSON.parse(messageContent) : messageContent
        }
      }

      // 处理C字段格式
      if (parsedData && parsedData.C) {
        parsedData = typeof parsedData.C === 'string' ? JSON.parse(parsedData.C) : parsedData.C
      }

      if (!parsedData || typeof parsedData !== 'object') return

      // 处理数据更新
      Object.entries(parsedData).forEach(([key, value]) => {
        const boolValue = Boolean(value)

        // 清理键名
        let cleanKey = key.replace(/\\u0022/g, '"').replace(/^"/, '').replace(/"$/, '')
        if (cleanKey.startsWith('S_')) cleanKey = cleanKey.substring(2)

        // 处理称重相关状态
        if (cleanKey.includes('內桶开盖工位称重清零') || key.includes('Manu_內桶开盖工位称重清零')) {
          buttonStates['內桶开盖工位称重清零']['称重清零'] = boolValue
        }
        // 处理读写卡状态
        else if (cleanKey.includes('内桶开盖工位读写卡') || key.includes('Manu_内桶开盖工位读写卡')) {
          buttonStates['内桶开盖工位读写卡']['读写卡'] = boolValue
        }
        // 处理密码盖控制状态
        else if (cleanKey.includes('內桶开盖工位密码盖遥控开锁') || key.includes('Manu_內桶开盖工位密码盖遥控开锁')) {
          buttonStates['內桶开盖工位密码盖遥控']['开锁'] = boolValue
          if (boolValue) buttonStates['內桶开盖工位密码盖遥控']['闭锁'] = false
        }
        else if (cleanKey.includes('內桶开盖工位密码盖遥控闭锁') || key.includes('Manu_內桶开盖工位密码盖遥控闭锁')) {
          buttonStates['內桶开盖工位密码盖遥控']['闭锁'] = boolValue
          if (boolValue) buttonStates['內桶开盖工位密码盖遥控']['开锁'] = false
        }
        // 处理气缸状态
        else if (cleanKey.includes('内桶开盖工位夹盖气缸伸出') || key.includes('Manu_内桶开盖工位夹盖气缸伸出')) {
          buttonStates['内桶开盖工位夹盖气缸']['伸出'] = boolValue
          if (boolValue) buttonStates['内桶开盖工位夹盖气缸']['缩回'] = false
        }
        else if (cleanKey.includes('内桶开盖工位夹盖气缸缩回') || key.includes('Manu_内桶开盖工位夹盖气缸缩回')) {
          buttonStates['内桶开盖工位夹盖气缸']['缩回'] = boolValue
          if (boolValue) buttonStates['内桶开盖工位夹盖气缸']['伸出'] = false
        }
        // 其他状态更新逻辑...
      })
    } catch (error) {
      console.error('称重处理实时数据异常:', error)
    }
  }

  // 简化的toggleSingleButton方法，直接调用sendControlCommand
  const toggleSingleButton = async (deviceType: string, action: string) => {
    await sendControlCommand(deviceType, action)
  }

  // 组件挂载时设置监听
  onMounted(() => {
    // 注册监听器
    const registerListener = () => {
      signalR.on('Message', handleRealtimeData)
    }

    // 如果已经连接，直接注册监听器
    if (signalR.isConnected.value) {
      registerListener()
    } else {
      // 定期检查连接状态
      const connectionCheckInterval = setInterval(() => {
        if (signalR.isConnected.value) {
          registerListener()
          clearInterval(connectionCheckInterval)
        }
      }, 1000)

      // 超时后停止检查
      setTimeout(() => {
        clearInterval(connectionCheckInterval)
      }, 5000)
    }
  })

  // 组件卸载时清理监听
  onUnmounted(() => {
    signalR.off('Message', handleRealtimeData)
  })

  return {
    buttonStates,
    deviceStatus,
    isLoading,
    toggleSingleButton
  }
}
