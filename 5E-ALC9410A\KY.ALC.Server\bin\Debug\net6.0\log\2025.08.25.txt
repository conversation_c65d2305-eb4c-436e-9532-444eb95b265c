2025/08/25 08:55:56.370 开始加载插件。
2025/08/25 08:55:56.886 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.ALC.Service.dll'加载完成。
2025/08/25 08:55:57.504 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.ALC.www.dll'加载完成。
2025/08/25 08:55:58.374 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.Common.dll'加载完成。
2025/08/25 08:55:58.485 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.Cookie.dll'加载完成。
2025/08/25 08:55:59.269 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.www.dll'加载完成。
2025/08/25 08:55:59.378 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.SignalR.dll'加载完成。
2025/08/25 08:55:59.554 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.WebSocket.dll'加载完成。
2025/08/25 08:55:59.632 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.www.dll'加载完成。
2025/08/25 08:56:00.020 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.Client.dll'加载完成。
2025/08/25 08:56:00.022 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.WebSocket.dll'加载完成。
2025/08/25 08:56:00.434 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.www.dll'加载完成。
2025/08/25 08:56:00.436 插件加载完成。
2025/08/25 08:56:00.554 启动websocket自动推送服务
2025/08/25 08:56:00.573 ALC:ALC数据提供者已初始化
2025/08/25 08:56:00.574 启动websocket自动推送服务守护线程
2025/08/25 08:56:00.576 ALC:ALC SignalR组件注册成功
2025/08/25 08:56:01.783 启动SignalR自动推送服务监控线程
2025/08/25 08:56:01.872 Now listening on: http://[::]:5009. 
2025/08/25 08:56:01.874 Application started. Press Ctrl+C to shut down. 
2025/08/25 08:56:01.875 Hosting environment: Production. 
2025/08/25 08:56:01.875 Content root path: E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\. 
2025/08/25 08:56:03.147 [ErrCode:E9999]未知错误|ALC:PLC连接失败
System.Exception: 连接************:502超时2s
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 112
   at DAL.ALC.Service.ServiceMain.Init() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 289
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:56:03.965 ALC:初始化步骤3，数据缓存器构建成功，点表长度391
2025/08/25 08:56:03.966 ALC:初始化步骤4，服务类初始化成功
2025/08/25 08:56:03.967 ALC:后台服务启动成功
2025/08/25 08:56:04.010 ALC:启动自动报警监控
2025/08/25 08:56:04.011 ALC:启动获取数据服务
2025/08/25 08:56:04.011 ALC:自动报警监控被关闭
2025/08/25 08:56:04.494 ALC:启动自动重启服务功能
2025/08/25 08:56:04.494 ALC:启动自动重连服务守护线程
2025/08/25 08:56:05.980 ALC:1#PLC点位开始轮询
2025/08/25 08:56:05.980 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:05.981 ALC:后台服务关闭
2025/08/25 08:56:05.982 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:06.029 ALC:获取数据服务停止
2025/08/25 08:56:14.500 ALC:后台服务启动成功
2025/08/25 08:56:14.511 ALC:启动自动报警监控
2025/08/25 08:56:14.546 ALC:自动报警监控被关闭
2025/08/25 08:56:14.547 ALC:自动重启服务成功
2025/08/25 08:56:14.547 ALC:启动获取数据服务
2025/08/25 08:56:16.523 ALC:1#PLC点位开始轮询
2025/08/25 08:56:16.523 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:16.524 ALC:后台服务关闭
2025/08/25 08:56:16.524 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:16.558 ALC:获取数据服务停止
2025/08/25 08:56:24.562 ALC:后台服务启动成功
2025/08/25 08:56:24.571 ALC:启动自动报警监控
2025/08/25 08:56:24.574 ALC:自动报警监控被关闭
2025/08/25 08:56:24.574 ALC:启动获取数据服务
2025/08/25 08:56:24.575 ALC:自动重启服务成功
2025/08/25 08:56:26.571 ALC:1#PLC点位开始轮询
2025/08/25 08:56:26.572 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:26.573 ALC:后台服务关闭
2025/08/25 08:56:26.573 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:26.587 ALC:获取数据服务停止
2025/08/25 08:56:33.662 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 08:56:33.781 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 117.7890ms. 
2025/08/25 08:56:34.243 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 08:56:34.253 CORS policy execution successful. 
2025/08/25 08:56:34.257 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 14.1519ms. 
2025/08/25 08:56:34.261 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 08:56:34.262 CORS policy execution successful. 
2025/08/25 08:56:34.265 Executing endpoint '/socket/negotiate'. 
2025/08/25 08:56:34.302 Executed endpoint '/socket/negotiate'. 
2025/08/25 08:56:34.303 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 42.0943ms. 
2025/08/25 08:56:34.309 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=l9EIlcOitSrXyL8-SZzQsw - -. 
2025/08/25 08:56:34.310 CORS policy execution successful. 
2025/08/25 08:56:34.310 Executing endpoint '/socket'. 
2025/08/25 08:56:34.581 ALC:后台服务启动成功
2025/08/25 08:56:34.593 ALC:启动自动报警监控
2025/08/25 08:56:34.595 ALC:自动重启服务成功
2025/08/25 08:56:34.596 ALC:自动报警监控被关闭
2025/08/25 08:56:34.596 ALC:启动获取数据服务
2025/08/25 08:56:36.590 ALC:1#PLC点位开始轮询
2025/08/25 08:56:36.590 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:36.591 ALC:后台服务关闭
2025/08/25 08:56:36.592 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:36.620 ALC:获取数据服务停止
2025/08/25 08:56:44.609 ALC:后台服务启动成功
2025/08/25 08:56:44.619 ALC:自动重启服务成功
2025/08/25 08:56:44.620 ALC:启动自动报警监控
2025/08/25 08:56:44.621 ALC:启动获取数据服务
2025/08/25 08:56:44.622 ALC:自动报警监控被关闭
2025/08/25 08:56:46.014 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/User/Login application/json 84. 
2025/08/25 08:56:46.015 CORS policy execution successful. 
2025/08/25 08:56:46.260 查询到用户:1
2025/08/25 08:56:46.380 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/User/Login application/json 84 - 200 - application/json;+charset=utf-8 365.2787ms. 
2025/08/25 08:56:46.622 ALC:1#PLC点位开始轮询
2025/08/25 08:56:46.622 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:46.623 ALC:后台服务关闭
2025/08/25 08:56:46.624 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:46.637 ALC:获取数据服务停止
2025/08/25 08:56:46.705 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/UBAC/GetUserEffectivePermissions application/json 19. 
2025/08/25 08:56:46.705 CORS policy execution successful. 
2025/08/25 08:56:46.774 用户角色为 System.Collections.Generic.List`1[COM.IFP.SqlSugarN.Field`1[System.String]]
2025/08/25 08:56:46.972 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/UBAC/GetUserEffectivePermissions application/json 19 - 200 - application/json;+charset=utf-8 267.4465ms. 
2025/08/25 08:56:46.986 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 08:56:46.987 CORS policy execution successful. 
2025/08/25 08:56:47.054 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 08:56:47.258 用户 admin 是超级管理员，返回所有菜单
2025/08/25 08:56:47.296 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 08:56:47.304 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 317.5826ms. 
2025/08/25 08:56:54.633 ALC:后台服务启动成功
2025/08/25 08:56:54.644 ALC:自动重启服务成功
2025/08/25 08:56:54.644 ALC:启动自动报警监控
2025/08/25 08:56:54.645 ALC:自动报警监控被关闭
2025/08/25 08:56:54.645 ALC:启动获取数据服务
2025/08/25 08:56:56.650 ALC:1#PLC点位开始轮询
2025/08/25 08:56:56.651 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:56.652 ALC:后台服务关闭
2025/08/25 08:56:56.653 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:57.659 ALC:获取数据服务停止
2025/08/25 08:57:05.712 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 你的主机中的软件中止了一个已建立的连接。.
 ---> System.Net.Sockets.SocketException (10053): 你的主机中的软件中止了一个已建立的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 124
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 08:57:05.717 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: Unable to write data to the transport connection: 你的主机中的软件中止了一个已建立的连接。.
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:57:15.765 ALC:后台服务启动成功
2025/08/25 08:57:15.775 ALC:启动自动报警监控
2025/08/25 08:57:15.803 ALC:自动报警监控被关闭
2025/08/25 08:57:15.804 ALC:自动重启服务成功
2025/08/25 08:57:15.804 ALC:启动获取数据服务
2025/08/25 08:57:17.777 ALC:1#PLC点位开始轮询
2025/08/25 08:57:17.778 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:17.779 ALC:后台服务关闭
2025/08/25 08:57:17.779 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:17.842 ALC:获取数据服务停止
2025/08/25 08:57:25.821 ALC:后台服务启动成功
2025/08/25 08:57:25.833 ALC:自动重启服务成功
2025/08/25 08:57:25.834 ALC:启动自动报警监控
2025/08/25 08:57:25.834 ALC:自动报警监控被关闭
2025/08/25 08:57:25.835 ALC:启动获取数据服务
2025/08/25 08:57:27.829 ALC:1#PLC点位开始轮询
2025/08/25 08:57:27.830 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:27.832 ALC:后台服务关闭
2025/08/25 08:57:27.832 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:27.847 ALC:获取数据服务停止
2025/08/25 08:57:36.081 ALC:后台服务启动成功
2025/08/25 08:57:36.100 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:57:36.107 ALC:自动重启服务成功
2025/08/25 08:57:36.108 ALC:启动自动报警监控
2025/08/25 08:57:36.108 ALC:自动报警监控被关闭
2025/08/25 08:57:36.109 ALC:启动获取数据服务
2025/08/25 08:57:38.102 ALC:1#PLC点位开始轮询
2025/08/25 08:57:38.103 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:38.104 ALC:后台服务关闭
2025/08/25 08:57:38.104 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:38.120 ALC:获取数据服务停止
2025/08/25 08:57:46.117 ALC:后台服务启动成功
2025/08/25 08:57:46.126 ALC:启动自动报警监控
2025/08/25 08:57:46.130 ALC:自动报警监控被关闭
2025/08/25 08:57:46.130 ALC:自动重启服务成功
2025/08/25 08:57:46.131 ALC:启动获取数据服务
2025/08/25 08:57:48.134 ALC:1#PLC点位开始轮询
2025/08/25 08:57:48.135 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:48.135 ALC:后台服务关闭
2025/08/25 08:57:48.135 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:48.162 ALC:获取数据服务停止
2025/08/25 08:57:56.136 ALC:后台服务启动成功
2025/08/25 08:57:56.146 ALC:自动重启服务成功
2025/08/25 08:57:56.147 ALC:启动自动报警监控
2025/08/25 08:57:56.147 ALC:自动报警监控被关闭
2025/08/25 08:57:56.147 ALC:启动获取数据服务
2025/08/25 08:57:58.155 ALC:1#PLC点位开始轮询
2025/08/25 08:57:58.160 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:58.161 ALC:后台服务关闭
2025/08/25 08:57:58.162 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:59.169 ALC:获取数据服务停止
2025/08/25 08:58:06.156 ALC:后台服务启动成功
2025/08/25 08:58:06.170 ALC:自动重启服务成功
2025/08/25 08:58:06.171 ALC:启动自动报警监控
2025/08/25 08:58:06.171 ALC:自动报警监控被关闭
2025/08/25 08:58:06.172 ALC:启动获取数据服务
2025/08/25 08:58:10.602 ALC:1#PLC点位开始轮询
2025/08/25 08:58:10.618 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:10.628 ALC:后台服务关闭
2025/08/25 08:58:10.628 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:10.633 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:58:11.599 ALC:获取数据服务停止
2025/08/25 08:58:16.182 ALC:后台服务启动成功
2025/08/25 08:58:16.193 ALC:启动自动报警监控
2025/08/25 08:58:16.229 ALC:自动报警监控被关闭
2025/08/25 08:58:16.229 ALC:自动重启服务成功
2025/08/25 08:58:16.230 ALC:启动获取数据服务
2025/08/25 08:58:18.197 ALC:1#PLC点位开始轮询
2025/08/25 08:58:18.198 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:18.198 ALC:后台服务关闭
2025/08/25 08:58:18.199 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:18.248 ALC:获取数据服务停止
2025/08/25 08:58:26.239 ALC:后台服务启动成功
2025/08/25 08:58:26.249 ALC:自动重启服务成功
2025/08/25 08:58:26.250 ALC:启动自动报警监控
2025/08/25 08:58:26.250 ALC:自动报警监控被关闭
2025/08/25 08:58:26.250 ALC:启动获取数据服务
2025/08/25 08:58:28.250 ALC:1#PLC点位开始轮询
2025/08/25 08:58:28.251 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:28.255 ALC:后台服务关闭
2025/08/25 08:58:28.256 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:28.260 ALC:获取数据服务停止
2025/08/25 08:58:36.255 ALC:后台服务启动成功
2025/08/25 08:58:36.266 ALC:自动重启服务成功
2025/08/25 08:58:36.267 ALC:启动自动报警监控
2025/08/25 08:58:36.267 ALC:自动报警监控被关闭
2025/08/25 08:58:36.268 ALC:启动获取数据服务
2025/08/25 08:58:38.262 ALC:1#PLC点位开始轮询
2025/08/25 08:58:38.263 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:38.264 ALC:后台服务关闭
2025/08/25 08:58:38.264 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:38.278 ALC:获取数据服务停止
2025/08/25 08:58:46.274 ALC:后台服务启动成功
2025/08/25 08:58:46.286 ALC:自动重启服务成功
2025/08/25 08:58:46.286 ALC:启动自动报警监控
2025/08/25 08:58:46.287 ALC:自动报警监控被关闭
2025/08/25 08:58:46.287 ALC:启动获取数据服务
2025/08/25 08:58:48.297 ALC:1#PLC点位开始轮询
2025/08/25 08:58:48.298 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:48.298 ALC:后台服务关闭
2025/08/25 08:58:48.299 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:49.300 ALC:获取数据服务停止
2025/08/25 08:58:56.291 ALC:后台服务启动成功
2025/08/25 08:58:56.300 ALC:启动自动报警监控
2025/08/25 08:58:56.302 ALC:自动报警监控被关闭
2025/08/25 08:58:56.303 ALC:启动获取数据服务
2025/08/25 08:58:56.303 ALC:自动重启服务成功
2025/08/25 08:58:58.306 ALC:1#PLC点位开始轮询
2025/08/25 08:58:58.307 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:58.307 ALC:后台服务关闭
2025/08/25 08:58:58.307 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:58.318 ALC:获取数据服务停止
2025/08/25 08:59:06.310 ALC:后台服务启动成功
2025/08/25 08:59:06.321 ALC:自动重启服务成功
2025/08/25 08:59:06.322 ALC:启动自动报警监控
2025/08/25 08:59:06.322 ALC:自动报警监控被关闭
2025/08/25 08:59:06.323 ALC:启动获取数据服务
2025/08/25 08:59:08.325 ALC:1#PLC点位开始轮询
2025/08/25 08:59:08.328 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:08.329 ALC:后台服务关闭
2025/08/25 08:59:08.329 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:08.340 ALC:获取数据服务停止
2025/08/25 08:59:16.324 ALC:后台服务启动成功
2025/08/25 08:59:16.337 ALC:自动重启服务成功
2025/08/25 08:59:16.338 ALC:启动自动报警监控
2025/08/25 08:59:16.338 ALC:自动报警监控被关闭
2025/08/25 08:59:16.339 ALC:启动获取数据服务
2025/08/25 08:59:18.343 ALC:1#PLC点位开始轮询
2025/08/25 08:59:18.344 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:18.345 ALC:后台服务关闭
2025/08/25 08:59:18.345 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:19.345 ALC:获取数据服务停止
2025/08/25 08:59:26.351 ALC:后台服务启动成功
2025/08/25 08:59:26.359 ALC:启动自动报警监控
2025/08/25 08:59:26.387 ALC:自动报警监控被关闭
2025/08/25 08:59:26.388 ALC:自动重启服务成功
2025/08/25 08:59:26.388 ALC:启动获取数据服务
2025/08/25 08:59:28.371 ALC:1#PLC点位开始轮询
2025/08/25 08:59:28.371 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:28.372 ALC:后台服务关闭
2025/08/25 08:59:28.372 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:28.403 ALC:获取数据服务停止
2025/08/25 08:59:36.400 ALC:后台服务启动成功
2025/08/25 08:59:36.408 ALC:启动自动报警监控
2025/08/25 08:59:36.441 ALC:自动报警监控被关闭
2025/08/25 08:59:36.441 ALC:自动重启服务成功
2025/08/25 08:59:36.442 ALC:启动获取数据服务
2025/08/25 08:59:38.165 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:59:38.409 ALC:1#PLC点位开始轮询
2025/08/25 08:59:38.409 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:38.410 ALC:后台服务关闭
2025/08/25 08:59:38.411 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:39.153 ALC:获取数据服务停止
2025/08/25 08:59:46.460 ALC:后台服务启动成功
2025/08/25 08:59:46.470 ALC:自动重启服务成功
2025/08/25 08:59:46.471 ALC:启动自动报警监控
2025/08/25 08:59:46.471 ALC:自动报警监控被关闭
2025/08/25 08:59:46.472 ALC:启动获取数据服务
2025/08/25 08:59:48.480 ALC:1#PLC点位开始轮询
2025/08/25 08:59:48.481 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:48.481 ALC:后台服务关闭
2025/08/25 08:59:48.482 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:48.483 ALC:获取数据服务停止
2025/08/25 08:59:51.861 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:59:56.838 ALC:后台服务启动成功
2025/08/25 08:59:56.848 ALC:自动重启服务成功
2025/08/25 08:59:56.849 ALC:启动自动报警监控
2025/08/25 08:59:56.849 ALC:自动报警监控被关闭
2025/08/25 08:59:56.849 ALC:启动获取数据服务
2025/08/25 08:59:58.859 ALC:1#PLC点位开始轮询
2025/08/25 08:59:58.860 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:58.861 ALC:后台服务关闭
2025/08/25 08:59:58.862 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:58.872 ALC:获取数据服务停止
2025/08/25 09:00:08.071 ALC:后台服务启动成功
2025/08/25 09:00:08.094 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:00:08.096 ALC:自动重启服务成功
2025/08/25 09:00:08.097 ALC:启动自动报警监控
2025/08/25 09:00:08.097 ALC:自动报警监控被关闭
2025/08/25 09:00:08.098 ALC:启动获取数据服务
2025/08/25 09:00:10.097 ALC:1#PLC点位开始轮询
2025/08/25 09:00:10.098 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:10.099 ALC:后台服务关闭
2025/08/25 09:00:10.100 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:10.106 ALC:获取数据服务停止
2025/08/25 09:00:18.109 ALC:后台服务启动成功
2025/08/25 09:00:18.155 ALC:自动重启服务成功
2025/08/25 09:00:18.184 ALC:启动自动报警监控
2025/08/25 09:00:18.191 ALC:自动报警监控被关闭
2025/08/25 09:00:18.193 ALC:启动获取数据服务
2025/08/25 09:00:20.147 ALC:1#PLC点位开始轮询
2025/08/25 09:00:20.148 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:20.149 ALC:后台服务关闭
2025/08/25 09:00:20.149 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:20.211 ALC:获取数据服务停止
2025/08/25 09:00:28.201 ALC:后台服务启动成功
2025/08/25 09:00:28.227 ALC:启动自动报警监控
2025/08/25 09:00:28.231 ALC:自动报警监控被关闭
2025/08/25 09:00:28.233 ALC:自动重启服务成功
2025/08/25 09:00:28.233 ALC:启动获取数据服务
2025/08/25 09:00:30.232 ALC:1#PLC点位开始轮询
2025/08/25 09:00:30.294 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:30.362 ALC:后台服务关闭
2025/08/25 09:00:30.363 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:31.307 ALC:获取数据服务停止
2025/08/25 09:00:38.245 ALC:后台服务启动成功
2025/08/25 09:00:38.281 ALC:启动自动报警监控
2025/08/25 09:00:38.284 ALC:自动报警监控被关闭
2025/08/25 09:00:38.287 ALC:自动重启服务成功
2025/08/25 09:00:38.288 ALC:启动获取数据服务
2025/08/25 09:00:40.287 ALC:1#PLC点位开始轮询
2025/08/25 09:00:40.292 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:40.294 ALC:后台服务关闭
2025/08/25 09:00:40.296 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:40.383 ALC:获取数据服务停止
2025/08/25 09:00:49.094 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:00:59.216 ALC:后台服务启动成功
2025/08/25 09:01:00.457 ALC:启动自动报警监控
2025/08/25 09:01:02.285 ALC:自动重启服务成功
2025/08/25 09:01:02.654 ALC:自动报警监控被关闭
2025/08/25 09:01:02.655 ALC:1#PLC点位开始轮询
2025/08/25 09:01:02.656 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:02.657 ALC:后台服务关闭
2025/08/25 09:01:02.658 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:02.803 ALC:启动获取数据服务
2025/08/25 09:01:02.820 ALC:获取数据服务停止
2025/08/25 09:01:12.684 ALC:后台服务启动成功
2025/08/25 09:01:12.713 ALC:自动重启服务成功
2025/08/25 09:01:12.714 ALC:启动自动报警监控
2025/08/25 09:01:12.716 ALC:自动报警监控被关闭
2025/08/25 09:01:12.717 ALC:启动获取数据服务
2025/08/25 09:01:14.702 ALC:1#PLC点位开始轮询
2025/08/25 09:01:14.703 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:14.704 ALC:后台服务关闭
2025/08/25 09:01:14.705 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:14.747 ALC:获取数据服务停止
2025/08/25 09:01:22.729 ALC:后台服务启动成功
2025/08/25 09:01:22.816 ALC:启动自动报警监控
2025/08/25 09:01:22.821 ALC:自动报警监控被关闭
2025/08/25 09:01:22.825 ALC:自动重启服务成功
2025/08/25 09:01:22.827 ALC:启动获取数据服务
2025/08/25 09:01:24.793 ALC:1#PLC点位开始轮询
2025/08/25 09:01:24.794 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:24.795 ALC:后台服务关闭
2025/08/25 09:01:24.796 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:24.854 ALC:获取数据服务停止
2025/08/25 09:01:32.842 ALC:后台服务启动成功
2025/08/25 09:01:32.859 ALC:启动自动报警监控
2025/08/25 09:01:32.860 ALC:自动报警监控被关闭
2025/08/25 09:01:32.868 ALC:自动重启服务成功
2025/08/25 09:01:32.869 ALC:启动获取数据服务
2025/08/25 09:01:34.854 ALC:1#PLC点位开始轮询
2025/08/25 09:01:34.855 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:34.856 ALC:后台服务关闭
2025/08/25 09:01:34.857 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:34.885 ALC:获取数据服务停止
2025/08/25 09:01:42.964 ALC:后台服务启动成功
2025/08/25 09:01:43.111 ALC:自动重启服务成功
2025/08/25 09:01:43.112 ALC:启动自动报警监控
2025/08/25 09:01:43.113 ALC:自动报警监控被关闭
2025/08/25 09:01:43.113 ALC:启动获取数据服务
2025/08/25 09:01:45.099 ALC:1#PLC点位开始轮询
2025/08/25 09:01:45.101 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:45.122 ALC:后台服务关闭
2025/08/25 09:01:45.128 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:45.141 ALC:获取数据服务停止
2025/08/25 09:01:53.171 ALC:后台服务启动成功
2025/08/25 09:01:53.282 ALC:自动重启服务成功
2025/08/25 09:01:53.772 ALC:启动获取数据服务
2025/08/25 09:01:53.773 ALC:启动自动报警监控
2025/08/25 09:01:53.774 ALC:自动报警监控被关闭
2025/08/25 09:01:55.784 ALC:1#PLC点位开始轮询
2025/08/25 09:01:55.796 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:55.797 ALC:后台服务关闭
2025/08/25 09:01:55.798 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:56.939 ALC:获取数据服务停止
2025/08/25 09:02:03.369 ALC:后台服务启动成功
2025/08/25 09:02:03.392 ALC:自动重启服务成功
2025/08/25 09:02:03.393 ALC:启动自动报警监控
2025/08/25 09:02:03.394 ALC:自动报警监控被关闭
2025/08/25 09:02:03.400 ALC:启动获取数据服务
2025/08/25 09:02:05.395 ALC:1#PLC点位开始轮询
2025/08/25 09:02:05.472 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:05.635 ALC:后台服务关闭
2025/08/25 09:02:05.716 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:06.448 ALC:获取数据服务停止
2025/08/25 09:02:13.420 ALC:后台服务启动成功
2025/08/25 09:02:13.447 ALC:启动自动报警监控
2025/08/25 09:02:13.447 ALC:自动报警监控被关闭
2025/08/25 09:02:13.458 ALC:自动重启服务成功
2025/08/25 09:02:13.459 ALC:启动获取数据服务
2025/08/25 09:02:15.450 ALC:1#PLC点位开始轮询
2025/08/25 09:02:15.450 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:15.451 ALC:后台服务关闭
2025/08/25 09:02:15.453 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:15.482 ALC:获取数据服务停止
2025/08/25 09:02:23.516 ALC:后台服务启动成功
2025/08/25 09:02:23.715 ALC:启动自动报警监控
2025/08/25 09:02:23.715 ALC:自动报警监控被关闭
2025/08/25 09:02:23.733 ALC:自动重启服务成功
2025/08/25 09:02:23.753 ALC:启动获取数据服务
2025/08/25 09:02:25.707 ALC:1#PLC点位开始轮询
2025/08/25 09:02:25.708 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:25.709 ALC:后台服务关闭
2025/08/25 09:02:25.709 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:25.769 ALC:获取数据服务停止
2025/08/25 09:02:33.786 ALC:后台服务启动成功
2025/08/25 09:02:33.905 ALC:启动自动报警监控
2025/08/25 09:02:33.905 ALC:自动报警监控被关闭
2025/08/25 09:02:33.914 ALC:自动重启服务成功
2025/08/25 09:02:33.919 ALC:启动获取数据服务
2025/08/25 09:02:35.882 ALC:1#PLC点位开始轮询
2025/08/25 09:02:35.884 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:35.886 ALC:后台服务关闭
2025/08/25 09:02:35.889 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:35.944 ALC:获取数据服务停止
2025/08/25 09:02:43.927 ALC:后台服务启动成功
2025/08/25 09:02:43.939 ALC:自动重启服务成功
2025/08/25 09:02:43.940 ALC:启动自动报警监控
2025/08/25 09:02:43.941 ALC:自动报警监控被关闭
2025/08/25 09:02:43.941 ALC:启动获取数据服务
2025/08/25 09:02:45.938 ALC:1#PLC点位开始轮询
2025/08/25 09:02:45.938 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:45.939 ALC:后台服务关闭
2025/08/25 09:02:45.939 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:45.953 ALC:获取数据服务停止
2025/08/25 09:02:53.945 ALC:后台服务启动成功
2025/08/25 09:02:53.954 ALC:启动自动报警监控
2025/08/25 09:02:53.956 ALC:自动报警监控被关闭
2025/08/25 09:02:53.957 ALC:自动重启服务成功
2025/08/25 09:02:53.957 ALC:启动获取数据服务
2025/08/25 09:02:55.954 ALC:1#PLC点位开始轮询
2025/08/25 09:02:55.955 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:55.956 ALC:后台服务关闭
2025/08/25 09:02:55.956 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:55.986 ALC:获取数据服务停止
2025/08/25 09:03:03.971 ALC:后台服务启动成功
2025/08/25 09:03:03.980 ALC:自动重启服务成功
2025/08/25 09:03:03.980 ALC:启动获取数据服务
2025/08/25 09:03:03.981 ALC:启动自动报警监控
2025/08/25 09:03:03.982 ALC:自动报警监控被关闭
2025/08/25 09:03:05.989 ALC:1#PLC点位开始轮询
2025/08/25 09:03:05.989 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:05.990 ALC:后台服务关闭
2025/08/25 09:03:05.991 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:07.005 ALC:获取数据服务停止
2025/08/25 09:03:13.990 ALC:后台服务启动成功
2025/08/25 09:03:14.002 ALC:启动自动报警监控
2025/08/25 09:03:14.039 ALC:自动报警监控被关闭
2025/08/25 09:03:14.040 ALC:自动重启服务成功
2025/08/25 09:03:14.040 ALC:启动获取数据服务
2025/08/25 09:03:16.000 ALC:1#PLC点位开始轮询
2025/08/25 09:03:16.001 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:16.002 ALC:后台服务关闭
2025/08/25 09:03:16.003 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:16.063 ALC:获取数据服务停止
2025/08/25 09:03:24.046 ALC:后台服务启动成功
2025/08/25 09:03:24.070 ALC:自动重启服务成功
2025/08/25 09:03:24.071 ALC:启动自动报警监控
2025/08/25 09:03:24.072 ALC:自动报警监控被关闭
2025/08/25 09:03:24.073 ALC:启动获取数据服务
2025/08/25 09:03:26.064 ALC:1#PLC点位开始轮询
2025/08/25 09:03:26.066 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:26.067 ALC:后台服务关闭
2025/08/25 09:03:26.068 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:26.084 ALC:获取数据服务停止
2025/08/25 09:03:34.089 ALC:后台服务启动成功
2025/08/25 09:03:34.104 ALC:启动自动报警监控
2025/08/25 09:03:34.105 ALC:自动报警监控被关闭
2025/08/25 09:03:34.109 ALC:自动重启服务成功
2025/08/25 09:03:34.110 ALC:启动获取数据服务
2025/08/25 09:03:36.101 ALC:1#PLC点位开始轮询
2025/08/25 09:03:36.102 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:36.103 ALC:后台服务关闭
2025/08/25 09:03:36.104 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:36.133 ALC:获取数据服务停止
2025/08/25 09:03:39.614 Executed endpoint '/socket'. 
2025/08/25 09:03:39.614 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=l9EIlcOitSrXyL8-SZzQsw - - - 101 - - 425305.1160ms. 
2025/08/25 09:03:40.986 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:40.988 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.8477ms. 
2025/08/25 09:03:41.206 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:41.207 CORS policy execution successful. 
2025/08/25 09:03:41.210 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:41.251 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:41.267 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:41.268 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 61.3888ms. 
2025/08/25 09:03:42.334 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:42.336 CORS policy execution successful. 
2025/08/25 09:03:42.336 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.9515ms. 
2025/08/25 09:03:42.347 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:42.348 CORS policy execution successful. 
2025/08/25 09:03:42.348 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:42.349 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:42.350 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 3.2582ms. 
2025/08/25 09:03:42.580 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=ppc8TZwqQBBNbq_yfh3VJg - -. 
2025/08/25 09:03:42.581 CORS policy execution successful. 
2025/08/25 09:03:42.587 Executing endpoint '/socket'. 
2025/08/25 09:03:44.188 ALC:后台服务启动成功
2025/08/25 09:03:44.205 ALC:自动重启服务成功
2025/08/25 09:03:44.206 ALC:启动自动报警监控
2025/08/25 09:03:44.206 ALC:自动报警监控被关闭
2025/08/25 09:03:44.207 ALC:启动获取数据服务
2025/08/25 09:03:46.212 ALC:1#PLC点位开始轮询
2025/08/25 09:03:46.212 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:46.214 ALC:后台服务关闭
2025/08/25 09:03:46.217 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:46.220 ALC:获取数据服务停止
2025/08/25 09:03:47.468 Executed endpoint '/socket'. 
2025/08/25 09:03:47.480 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=ppc8TZwqQBBNbq_yfh3VJg - - - 101 - - 4899.9960ms. 
2025/08/25 09:03:49.100 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:49.101 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.9091ms. 
2025/08/25 09:03:49.187 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:49.322 CORS policy execution successful. 
2025/08/25 09:03:49.330 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:49.468 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:49.527 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:49.556 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 368.9094ms. 
2025/08/25 09:03:50.207 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:50.207 CORS policy execution successful. 
2025/08/25 09:03:50.208 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.9608ms. 
2025/08/25 09:03:50.210 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:50.211 CORS policy execution successful. 
2025/08/25 09:03:50.211 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:50.211 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:50.212 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.4374ms. 
2025/08/25 09:03:50.378 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=qXYKFQ4i4cDI3w3YEfacRQ - -. 
2025/08/25 09:03:50.378 CORS policy execution successful. 
2025/08/25 09:03:50.378 Executing endpoint '/socket'. 
2025/08/25 09:03:54.518 ALC:后台服务启动成功
2025/08/25 09:03:54.533 ALC:自动重启服务成功
2025/08/25 09:03:54.534 ALC:启动自动报警监控
2025/08/25 09:03:54.534 ALC:自动报警监控被关闭
2025/08/25 09:03:54.535 ALC:启动获取数据服务
2025/08/25 09:03:56.535 ALC:1#PLC点位开始轮询
2025/08/25 09:03:56.536 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:56.536 ALC:后台服务关闭
2025/08/25 09:03:56.537 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:56.553 ALC:获取数据服务停止
2025/08/25 09:03:58.567 Executed endpoint '/socket'. 
2025/08/25 09:03:58.575 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=qXYKFQ4i4cDI3w3YEfacRQ - - - 101 - - 8197.4143ms. 
2025/08/25 09:03:59.355 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:59.360 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 4.3551ms. 
2025/08/25 09:03:59.415 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:59.417 CORS policy execution successful. 
2025/08/25 09:03:59.425 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:59.431 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:59.436 CORS policy execution successful. 
2025/08/25 09:03:59.436 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 5.6383ms. 
2025/08/25 09:03:59.443 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:59.445 CORS policy execution successful. 
2025/08/25 09:03:59.446 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:59.446 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:59.447 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 4.0172ms. 
2025/08/25 09:03:59.455 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=_GWYdp8lZYNIOXgUzvmpVA - -. 
2025/08/25 09:03:59.455 CORS policy execution successful. 
2025/08/25 09:03:59.462 Executing endpoint '/socket'. 
2025/08/25 09:03:59.462 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:59.481 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:59.482 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 66.7060ms. 
2025/08/25 09:04:04.742 ALC:后台服务启动成功
2025/08/25 09:04:04.756 ALC:启动自动报警监控
2025/08/25 09:04:04.760 ALC:自动报警监控被关闭
2025/08/25 09:04:04.760 ALC:自动重启服务成功
2025/08/25 09:04:04.761 ALC:启动获取数据服务
2025/08/25 09:04:06.765 ALC:1#PLC点位开始轮询
2025/08/25 09:04:06.766 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:06.766 ALC:后台服务关闭
2025/08/25 09:04:06.767 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:06.777 ALC:获取数据服务停止
2025/08/25 09:04:08.380 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:08.381 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.6434ms. 
2025/08/25 09:04:08.444 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:08.445 CORS policy execution successful. 
2025/08/25 09:04:08.448 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:08.454 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:08.454 CORS policy execution successful. 
2025/08/25 09:04:08.456 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.7627ms. 
2025/08/25 09:04:08.462 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:08.463 CORS policy execution successful. 
2025/08/25 09:04:08.463 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:08.464 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:08.464 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.9831ms. 
2025/08/25 09:04:08.466 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:08.481 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:08.482 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 37.8165ms. 
2025/08/25 09:04:08.485 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=M83jhn7Jk9TdrkE80u2G5Q - -. 
2025/08/25 09:04:08.485 CORS policy execution successful. 
2025/08/25 09:04:08.485 Executing endpoint '/socket'. 
2025/08/25 09:04:15.403 ALC:后台服务启动成功
2025/08/25 09:04:15.437 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:04:15.442 ALC:自动重启服务成功
2025/08/25 09:04:15.443 ALC:启动自动报警监控
2025/08/25 09:04:15.443 ALC:自动报警监控被关闭
2025/08/25 09:04:15.444 ALC:启动获取数据服务
2025/08/25 09:04:15.467 Executed endpoint '/socket'. 
2025/08/25 09:04:15.467 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=_GWYdp8lZYNIOXgUzvmpVA - - - 101 - - 16012.2765ms. 
2025/08/25 09:04:17.434 ALC:1#PLC点位开始轮询
2025/08/25 09:04:17.435 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:17.436 ALC:后台服务关闭
2025/08/25 09:04:17.436 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:17.453 ALC:获取数据服务停止
2025/08/25 09:04:18.360 Executed endpoint '/socket'. 
2025/08/25 09:04:18.364 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=M83jhn7Jk9TdrkE80u2G5Q - - - 101 - - 9878.5429ms. 
2025/08/25 09:04:19.675 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:19.676 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.2212ms. 
2025/08/25 09:04:19.757 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:19.759 CORS policy execution successful. 
2025/08/25 09:04:19.760 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:19.761 CORS policy execution successful. 
2025/08/25 09:04:19.761 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.8417ms. 
2025/08/25 09:04:19.764 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:19.766 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:19.772 CORS policy execution successful. 
2025/08/25 09:04:19.774 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:19.775 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:19.780 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 13.9621ms. 
2025/08/25 09:04:19.784 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:19.813 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=HiZdFmyuGr4IPM4-bp5YtQ - -. 
2025/08/25 09:04:19.815 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:19.818 CORS policy execution successful. 
2025/08/25 09:04:19.819 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 61.4407ms. 
2025/08/25 09:04:19.819 Executing endpoint '/socket'. 
2025/08/25 09:04:25.540 ALC:后台服务启动成功
2025/08/25 09:04:25.551 ALC:自动重启服务成功
2025/08/25 09:04:25.552 ALC:启动自动报警监控
2025/08/25 09:04:25.552 ALC:自动报警监控被关闭
2025/08/25 09:04:25.553 ALC:启动获取数据服务
2025/08/25 09:04:27.555 ALC:1#PLC点位开始轮询
2025/08/25 09:04:27.558 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:27.559 ALC:后台服务关闭
2025/08/25 09:04:27.560 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:27.570 ALC:获取数据服务停止
2025/08/25 09:04:35.566 ALC:后台服务启动成功
2025/08/25 09:04:35.577 ALC:自动重启服务成功
2025/08/25 09:04:35.578 ALC:启动获取数据服务
2025/08/25 09:04:35.578 ALC:启动自动报警监控
2025/08/25 09:04:35.578 ALC:自动报警监控被关闭
2025/08/25 09:04:36.414 Executed endpoint '/socket'. 
2025/08/25 09:04:36.421 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=HiZdFmyuGr4IPM4-bp5YtQ - - - 101 - - 16607.6176ms. 
2025/08/25 09:04:37.490 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:37.542 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 52.0583ms. 
2025/08/25 09:04:37.606 ALC:1#PLC点位开始轮询
2025/08/25 09:04:37.695 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:37.708 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:37.708 ALC:后台服务关闭
2025/08/25 09:04:37.710 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:37.730 CORS policy execution successful. 
2025/08/25 09:04:37.730 CORS policy execution successful. 
2025/08/25 09:04:37.731 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 22.4000ms. 
2025/08/25 09:04:37.730 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:37.791 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:37.791 CORS policy execution successful. 
2025/08/25 09:04:37.791 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:37.792 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:37.792 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:37.793 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.2966ms. 
2025/08/25 09:04:37.806 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=rIDOa_0olxvEN7BMZgLeRw - -. 
2025/08/25 09:04:37.813 CORS policy execution successful. 
2025/08/25 09:04:37.815 Executing endpoint '/socket'. 
2025/08/25 09:04:37.828 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:37.865 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:37.882 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 174.4951ms. 
2025/08/25 09:04:38.674 ALC:获取数据服务停止
2025/08/25 09:04:45.585 ALC:后台服务启动成功
2025/08/25 09:04:45.599 ALC:自动重启服务成功
2025/08/25 09:04:45.601 ALC:启动自动报警监控
2025/08/25 09:04:45.602 ALC:自动报警监控被关闭
2025/08/25 09:04:45.604 ALC:启动获取数据服务
2025/08/25 09:04:47.562 Executed endpoint '/socket'. 
2025/08/25 09:04:47.562 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=rIDOa_0olxvEN7BMZgLeRw - - - 101 - - 9756.5420ms. 
2025/08/25 09:04:47.604 ALC:1#PLC点位开始轮询
2025/08/25 09:04:47.606 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:47.637 ALC:后台服务关闭
2025/08/25 09:04:47.638 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:47.641 ALC:获取数据服务停止
2025/08/25 09:04:48.357 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:48.358 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.2130ms. 
2025/08/25 09:04:48.419 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:48.421 CORS policy execution successful. 
2025/08/25 09:04:48.423 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:48.427 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:48.429 CORS policy execution successful. 
2025/08/25 09:04:48.429 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.9837ms. 
2025/08/25 09:04:48.434 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:48.434 CORS policy execution successful. 
2025/08/25 09:04:48.435 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:48.436 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:48.437 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.9508ms. 
2025/08/25 09:04:48.452 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:48.457 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=K-iDHZ_j6rhbDKSvJburRQ - -. 
2025/08/25 09:04:48.458 CORS policy execution successful. 
2025/08/25 09:04:48.458 Executing endpoint '/socket'. 
2025/08/25 09:04:48.466 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:48.467 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 48.0129ms. 
2025/08/25 09:04:55.623 ALC:后台服务启动成功
2025/08/25 09:04:55.634 ALC:自动重启服务成功
2025/08/25 09:04:55.634 ALC:启动自动报警监控
2025/08/25 09:04:55.635 ALC:自动报警监控被关闭
2025/08/25 09:04:55.636 ALC:启动获取数据服务
2025/08/25 09:04:57.632 ALC:1#PLC点位开始轮询
2025/08/25 09:04:57.634 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:57.634 ALC:后台服务关闭
2025/08/25 09:04:57.635 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:57.648 ALC:获取数据服务停止
2025/08/25 09:05:05.638 ALC:后台服务启动成功
2025/08/25 09:05:05.646 ALC:启动自动报警监控
2025/08/25 09:05:05.684 ALC:自动报警监控被关闭
2025/08/25 09:05:05.685 ALC:自动重启服务成功
2025/08/25 09:05:05.686 ALC:启动获取数据服务
2025/08/25 09:05:07.645 ALC:1#PLC点位开始轮询
2025/08/25 09:05:07.646 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:07.646 ALC:后台服务关闭
2025/08/25 09:05:07.647 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:07.695 ALC:获取数据服务停止
2025/08/25 09:05:15.702 ALC:后台服务启动成功
2025/08/25 09:05:15.713 ALC:自动重启服务成功
2025/08/25 09:05:15.714 ALC:启动自动报警监控
2025/08/25 09:05:15.715 ALC:自动报警监控被关闭
2025/08/25 09:05:15.716 ALC:启动获取数据服务
2025/08/25 09:05:17.709 ALC:1#PLC点位开始轮询
2025/08/25 09:05:17.710 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:17.710 ALC:后台服务关闭
2025/08/25 09:05:17.710 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:17.740 ALC:获取数据服务停止
2025/08/25 09:05:25.725 ALC:后台服务启动成功
2025/08/25 09:05:25.736 ALC:自动重启服务成功
2025/08/25 09:05:25.737 ALC:启动自动报警监控
2025/08/25 09:05:25.737 ALC:自动报警监控被关闭
2025/08/25 09:05:25.738 ALC:启动获取数据服务
2025/08/25 09:05:27.742 ALC:1#PLC点位开始轮询
2025/08/25 09:05:27.743 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:27.745 ALC:后台服务关闭
2025/08/25 09:05:27.745 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:27.749 ALC:获取数据服务停止
2025/08/25 09:05:35.748 ALC:后台服务启动成功
2025/08/25 09:05:35.758 ALC:启动自动报警监控
2025/08/25 09:05:35.761 ALC:自动报警监控被关闭
2025/08/25 09:05:35.762 ALC:自动重启服务成功
2025/08/25 09:05:35.763 ALC:启动获取数据服务
2025/08/25 09:05:37.797 ALC:1#PLC点位开始轮询
2025/08/25 09:05:37.799 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:37.800 ALC:后台服务关闭
2025/08/25 09:05:37.800 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:38.802 ALC:获取数据服务停止
2025/08/25 09:05:45.767 ALC:后台服务启动成功
2025/08/25 09:05:45.781 ALC:自动重启服务成功
2025/08/25 09:05:45.782 ALC:启动自动报警监控
2025/08/25 09:05:45.782 ALC:自动报警监控被关闭
2025/08/25 09:05:45.782 ALC:启动获取数据服务
2025/08/25 09:05:47.789 ALC:1#PLC点位开始轮询
2025/08/25 09:05:47.792 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:47.793 ALC:后台服务关闭
2025/08/25 09:05:47.794 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:47.798 ALC:获取数据服务停止
2025/08/25 09:05:55.790 ALC:后台服务启动成功
2025/08/25 09:05:55.798 ALC:启动自动报警监控
2025/08/25 09:05:55.836 ALC:自动报警监控被关闭
2025/08/25 09:05:55.837 ALC:自动重启服务成功
2025/08/25 09:05:55.838 ALC:启动获取数据服务
2025/08/25 09:05:57.801 ALC:1#PLC点位开始轮询
2025/08/25 09:05:57.802 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:57.802 ALC:后台服务关闭
2025/08/25 09:05:57.803 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:57.861 ALC:获取数据服务停止
2025/08/25 09:06:05.854 ALC:后台服务启动成功
2025/08/25 09:06:05.865 ALC:自动重启服务成功
2025/08/25 09:06:05.866 ALC:启动自动报警监控
2025/08/25 09:06:05.867 ALC:自动报警监控被关闭
2025/08/25 09:06:05.867 ALC:启动获取数据服务
2025/08/25 09:06:07.865 ALC:1#PLC点位开始轮询
2025/08/25 09:06:07.866 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:07.867 ALC:后台服务关闭
2025/08/25 09:06:07.867 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:07.876 ALC:获取数据服务停止
2025/08/25 09:06:15.870 ALC:后台服务启动成功
2025/08/25 09:06:15.889 ALC:自动重启服务成功
2025/08/25 09:06:15.890 ALC:启动自动报警监控
2025/08/25 09:06:15.891 ALC:自动报警监控被关闭
2025/08/25 09:06:15.892 ALC:启动获取数据服务
2025/08/25 09:06:17.884 ALC:1#PLC点位开始轮询
2025/08/25 09:06:17.884 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:17.885 ALC:后台服务关闭
2025/08/25 09:06:17.886 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:17.902 ALC:获取数据服务停止
2025/08/25 09:06:25.901 ALC:后台服务启动成功
2025/08/25 09:06:25.911 ALC:启动自动报警监控
2025/08/25 09:06:25.913 ALC:自动报警监控被关闭
2025/08/25 09:06:25.913 ALC:自动重启服务成功
2025/08/25 09:06:25.914 ALC:启动获取数据服务
2025/08/25 09:06:27.910 ALC:1#PLC点位开始轮询
2025/08/25 09:06:27.910 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:27.911 ALC:后台服务关闭
2025/08/25 09:06:27.911 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:27.941 ALC:获取数据服务停止
2025/08/25 09:06:35.920 ALC:后台服务启动成功
2025/08/25 09:06:35.929 ALC:启动自动报警监控
2025/08/25 09:06:35.965 ALC:自动报警监控被关闭
2025/08/25 09:06:35.966 ALC:自动重启服务成功
2025/08/25 09:06:35.966 ALC:启动获取数据服务
2025/08/25 09:06:37.940 ALC:1#PLC点位开始轮询
2025/08/25 09:06:37.941 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:37.941 ALC:后台服务关闭
2025/08/25 09:06:37.942 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:37.974 ALC:获取数据服务停止
2025/08/25 09:06:45.980 ALC:后台服务启动成功
2025/08/25 09:06:45.995 ALC:自动重启服务成功
2025/08/25 09:06:45.996 ALC:启动自动报警监控
2025/08/25 09:06:45.997 ALC:自动报警监控被关闭
2025/08/25 09:06:45.998 ALC:启动获取数据服务
2025/08/25 09:06:47.995 ALC:1#PLC点位开始轮询
2025/08/25 09:06:47.996 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:47.996 ALC:后台服务关闭
2025/08/25 09:06:47.997 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:48.011 ALC:获取数据服务停止
2025/08/25 09:06:56.008 ALC:后台服务启动成功
2025/08/25 09:06:56.025 ALC:自动重启服务成功
2025/08/25 09:06:56.025 ALC:启动自动报警监控
2025/08/25 09:06:56.026 ALC:自动报警监控被关闭
2025/08/25 09:06:56.027 ALC:启动获取数据服务
2025/08/25 09:06:58.019 ALC:1#PLC点位开始轮询
2025/08/25 09:06:58.020 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:58.021 ALC:后台服务关闭
2025/08/25 09:06:58.022 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:58.051 ALC:获取数据服务停止
2025/08/25 09:07:06.037 ALC:后台服务启动成功
2025/08/25 09:07:06.048 ALC:自动重启服务成功
2025/08/25 09:07:06.049 ALC:启动自动报警监控
2025/08/25 09:07:06.050 ALC:自动报警监控被关闭
2025/08/25 09:07:06.051 ALC:启动获取数据服务
2025/08/25 09:07:08.047 ALC:1#PLC点位开始轮询
2025/08/25 09:07:08.048 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:08.049 ALC:后台服务关闭
2025/08/25 09:07:08.050 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:08.057 ALC:获取数据服务停止
2025/08/25 09:07:10.577 Executed endpoint '/socket'. 
2025/08/25 09:07:10.607 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=K-iDHZ_j6rhbDKSvJburRQ - - - 101 - - 142150.0756ms. 
2025/08/25 09:07:11.294 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:07:11.305 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 10.8118ms. 
2025/08/25 09:07:11.387 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:07:11.403 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:07:11.405 CORS policy execution successful. 
2025/08/25 09:07:11.407 CORS policy execution successful. 
2025/08/25 09:07:11.411 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:07:11.408 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 20.3194ms. 
2025/08/25 09:07:11.459 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:07:11.460 CORS policy execution successful. 
2025/08/25 09:07:11.460 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:07:11.461 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:07:11.461 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 39.4868ms. 
2025/08/25 09:07:11.484 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=nwK8WbtJPBQR3i7ddW3z1w - -. 
2025/08/25 09:07:11.485 CORS policy execution successful. 
2025/08/25 09:07:11.484 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:07:11.485 Executing endpoint '/socket'. 
2025/08/25 09:07:11.507 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:07:11.508 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 121.0083ms. 
2025/08/25 09:07:16.156 ALC:后台服务启动成功
2025/08/25 09:07:16.169 ALC:启动自动报警监控
2025/08/25 09:07:16.173 ALC:自动报警监控被关闭
2025/08/25 09:07:16.174 ALC:自动重启服务成功
2025/08/25 09:07:16.175 ALC:启动获取数据服务
2025/08/25 09:07:18.173 ALC:1#PLC点位开始轮询
2025/08/25 09:07:18.174 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:18.174 ALC:后台服务关闭
2025/08/25 09:07:18.174 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:18.204 ALC:获取数据服务停止
2025/08/25 09:07:26.180 ALC:后台服务启动成功
2025/08/25 09:07:26.189 ALC:启动自动报警监控
2025/08/25 09:07:26.191 ALC:自动报警监控被关闭
2025/08/25 09:07:26.192 ALC:自动重启服务成功
2025/08/25 09:07:26.192 ALC:启动获取数据服务
2025/08/25 09:07:28.198 ALC:1#PLC点位开始轮询
2025/08/25 09:07:28.199 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:28.200 ALC:后台服务关闭
2025/08/25 09:07:28.200 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:29.201 ALC:获取数据服务停止
2025/08/25 09:07:36.136 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:07:36.215 ALC:后台服务启动成功
2025/08/25 09:07:36.228 ALC:自动重启服务成功
2025/08/25 09:07:36.229 ALC:启动自动报警监控
2025/08/25 09:07:36.230 ALC:自动报警监控被关闭
2025/08/25 09:07:36.231 ALC:启动获取数据服务
2025/08/25 09:07:38.239 ALC:1#PLC点位开始轮询
2025/08/25 09:07:38.240 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:38.240 ALC:后台服务关闭
2025/08/25 09:07:38.241 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:38.253 ALC:获取数据服务停止
2025/08/25 09:07:46.248 ALC:后台服务启动成功
2025/08/25 09:07:46.312 ALC:启动自动报警监控
2025/08/25 09:07:46.322 ALC:自动重启服务成功
2025/08/25 09:07:46.323 ALC:自动报警监控被关闭
2025/08/25 09:07:46.329 ALC:启动获取数据服务
2025/08/25 09:07:47.660 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:07:47.669 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 12.2019ms. 
2025/08/25 09:07:47.736 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:07:47.738 CORS policy execution successful. 
2025/08/25 09:07:47.746 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:07:47.764 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:07:47.817 CORS policy execution successful. 
2025/08/25 09:07:47.822 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 59.3829ms. 
2025/08/25 09:07:47.838 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:07:47.839 CORS policy execution successful. 
2025/08/25 09:07:47.841 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:07:47.843 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:07:47.844 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 12.0459ms. 
2025/08/25 09:07:47.845 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:07:47.862 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=6oPfVr5eLZJbxQ2Pz61TGA - -. 
2025/08/25 09:07:47.863 CORS policy execution successful. 
2025/08/25 09:07:47.870 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:07:47.870 Executing endpoint '/socket'. 
2025/08/25 09:07:47.871 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 134.5047ms. 
2025/08/25 09:07:48.336 ALC:1#PLC点位开始轮询
2025/08/25 09:07:48.337 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:48.337 ALC:后台服务关闭
2025/08/25 09:07:48.338 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:48.345 ALC:获取数据服务停止
2025/08/25 09:07:52.521 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:07:52.539 Executed endpoint '/socket'. 
2025/08/25 09:07:52.540 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=nwK8WbtJPBQR3i7ddW3z1w - - - 101 - - 41055.9177ms. 
2025/08/25 09:07:57.497 ALC:后台服务启动成功
2025/08/25 09:07:57.507 ALC:自动重启服务成功
2025/08/25 09:07:57.507 ALC:启动自动报警监控
2025/08/25 09:07:57.508 ALC:自动报警监控被关闭
2025/08/25 09:07:57.509 ALC:启动获取数据服务
2025/08/25 09:07:59.512 ALC:1#PLC点位开始轮询
2025/08/25 09:07:59.512 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:59.513 ALC:后台服务关闭
2025/08/25 09:07:59.514 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:59.525 ALC:获取数据服务停止
2025/08/25 09:08:07.517 ALC:后台服务启动成功
2025/08/25 09:08:07.526 ALC:自动重启服务成功
2025/08/25 09:08:07.527 ALC:启动自动报警监控
2025/08/25 09:08:07.527 ALC:自动报警监控被关闭
2025/08/25 09:08:07.527 ALC:启动获取数据服务
2025/08/25 09:08:09.527 ALC:1#PLC点位开始轮询
2025/08/25 09:08:09.528 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:09.528 ALC:后台服务关闭
2025/08/25 09:08:09.529 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:09.542 ALC:获取数据服务停止
2025/08/25 09:08:17.535 ALC:后台服务启动成功
2025/08/25 09:08:17.547 ALC:自动重启服务成功
2025/08/25 09:08:17.547 ALC:启动自动报警监控
2025/08/25 09:08:17.548 ALC:自动报警监控被关闭
2025/08/25 09:08:17.549 ALC:启动获取数据服务
2025/08/25 09:08:19.550 ALC:1#PLC点位开始轮询
2025/08/25 09:08:19.553 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:19.554 ALC:后台服务关闭
2025/08/25 09:08:19.555 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:19.564 ALC:获取数据服务停止
2025/08/25 09:08:27.565 ALC:后台服务启动成功
2025/08/25 09:08:27.583 ALC:启动自动报警监控
2025/08/25 09:08:27.587 ALC:自动重启服务成功
2025/08/25 09:08:27.588 ALC:自动报警监控被关闭
2025/08/25 09:08:27.589 ALC:启动获取数据服务
2025/08/25 09:08:29.595 ALC:1#PLC点位开始轮询
2025/08/25 09:08:29.780 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:30.061 ALC:后台服务关闭
2025/08/25 09:08:30.407 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:30.676 ALC:获取数据服务停止
2025/08/25 09:08:42.218 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:08:53.710 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:09:04.041 ALC:后台服务启动成功
2025/08/25 09:09:04.054 ALC:启动自动报警监控
2025/08/25 09:09:04.058 ALC:启动获取数据服务
2025/08/25 09:09:04.059 ALC:自动重启服务成功
2025/08/25 09:09:04.060 ALC:自动报警监控被关闭
2025/08/25 09:09:06.056 ALC:1#PLC点位开始轮询
2025/08/25 09:09:06.056 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:06.057 ALC:后台服务关闭
2025/08/25 09:09:06.058 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:06.087 ALC:获取数据服务停止
2025/08/25 09:09:14.074 ALC:后台服务启动成功
2025/08/25 09:09:14.087 ALC:启动自动报警监控
2025/08/25 09:09:14.087 ALC:自动报警监控被关闭
2025/08/25 09:09:14.088 ALC:启动获取数据服务
2025/08/25 09:09:14.088 ALC:自动重启服务成功
2025/08/25 09:09:16.084 ALC:1#PLC点位开始轮询
2025/08/25 09:09:16.085 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:16.086 ALC:后台服务关闭
2025/08/25 09:09:16.086 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:16.099 ALC:获取数据服务停止
2025/08/25 09:09:24.102 ALC:后台服务启动成功
2025/08/25 09:09:24.112 ALC:启动自动报警监控
2025/08/25 09:09:24.146 ALC:自动报警监控被关闭
2025/08/25 09:09:24.147 ALC:自动重启服务成功
2025/08/25 09:09:24.147 ALC:启动获取数据服务
2025/08/25 09:09:26.115 ALC:1#PLC点位开始轮询
2025/08/25 09:09:26.115 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:26.116 ALC:后台服务关闭
2025/08/25 09:09:26.116 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:26.165 ALC:获取数据服务停止
2025/08/25 09:09:34.163 ALC:后台服务启动成功
2025/08/25 09:09:34.172 ALC:自动重启服务成功
2025/08/25 09:09:34.172 ALC:启动获取数据服务
2025/08/25 09:09:34.172 ALC:启动自动报警监控
2025/08/25 09:09:34.173 ALC:自动报警监控被关闭
2025/08/25 09:09:36.172 ALC:1#PLC点位开始轮询
2025/08/25 09:09:36.173 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:36.174 ALC:后台服务关闭
2025/08/25 09:09:36.174 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:36.187 ALC:获取数据服务停止
2025/08/25 09:09:44.183 ALC:后台服务启动成功
2025/08/25 09:09:44.196 ALC:自动重启服务成功
2025/08/25 09:09:44.197 ALC:启动自动报警监控
2025/08/25 09:09:44.197 ALC:自动报警监控被关闭
2025/08/25 09:09:44.198 ALC:启动获取数据服务
2025/08/25 09:09:46.196 ALC:1#PLC点位开始轮询
2025/08/25 09:09:46.197 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:46.197 ALC:后台服务关闭
2025/08/25 09:09:46.198 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:46.210 ALC:获取数据服务停止
2025/08/25 09:09:54.208 ALC:后台服务启动成功
2025/08/25 09:09:54.220 ALC:自动重启服务成功
2025/08/25 09:09:54.221 ALC:启动自动报警监控
2025/08/25 09:09:54.221 ALC:自动报警监控被关闭
2025/08/25 09:09:54.221 ALC:启动获取数据服务
2025/08/25 09:09:56.219 ALC:1#PLC点位开始轮询
2025/08/25 09:09:56.219 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:56.220 ALC:后台服务关闭
2025/08/25 09:09:56.220 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:56.235 ALC:获取数据服务停止
2025/08/25 09:10:04.228 ALC:后台服务启动成功
2025/08/25 09:10:04.240 ALC:自动重启服务成功
2025/08/25 09:10:04.241 ALC:启动自动报警监控
2025/08/25 09:10:04.241 ALC:自动报警监控被关闭
2025/08/25 09:10:04.241 ALC:启动获取数据服务
2025/08/25 09:10:06.238 ALC:1#PLC点位开始轮询
2025/08/25 09:10:06.239 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:06.239 ALC:后台服务关闭
2025/08/25 09:10:06.240 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:06.271 ALC:获取数据服务停止
2025/08/25 09:10:14.250 ALC:后台服务启动成功
2025/08/25 09:10:14.258 ALC:启动自动报警监控
2025/08/25 09:10:14.293 ALC:自动报警监控被关闭
2025/08/25 09:10:14.294 ALC:自动重启服务成功
2025/08/25 09:10:14.295 ALC:启动获取数据服务
2025/08/25 09:10:16.258 ALC:1#PLC点位开始轮询
2025/08/25 09:10:16.258 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:16.259 ALC:后台服务关闭
2025/08/25 09:10:16.259 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:16.306 ALC:获取数据服务停止
2025/08/25 09:10:24.303 ALC:后台服务启动成功
2025/08/25 09:10:24.312 ALC:启动自动报警监控
2025/08/25 09:10:24.337 ALC:自动报警监控被关闭
2025/08/25 09:10:24.337 ALC:自动重启服务成功
2025/08/25 09:10:24.338 ALC:启动获取数据服务
2025/08/25 09:10:26.321 ALC:1#PLC点位开始轮询
2025/08/25 09:10:26.322 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:26.322 ALC:后台服务关闭
2025/08/25 09:10:26.323 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:26.355 ALC:获取数据服务停止
2025/08/25 09:10:34.349 ALC:后台服务启动成功
2025/08/25 09:10:34.357 ALC:启动自动报警监控
2025/08/25 09:10:34.360 ALC:自动报警监控被关闭
2025/08/25 09:10:34.361 ALC:自动重启服务成功
2025/08/25 09:10:34.361 ALC:启动获取数据服务
2025/08/25 09:10:36.371 ALC:1#PLC点位开始轮询
2025/08/25 09:10:36.372 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:36.372 ALC:后台服务关闭
2025/08/25 09:10:36.373 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:37.376 ALC:获取数据服务停止
2025/08/25 09:10:44.378 ALC:后台服务启动成功
2025/08/25 09:10:44.386 ALC:启动自动报警监控
2025/08/25 09:10:44.388 ALC:自动报警监控被关闭
2025/08/25 09:10:44.389 ALC:自动重启服务成功
2025/08/25 09:10:44.389 ALC:启动获取数据服务
2025/08/25 09:10:46.398 ALC:1#PLC点位开始轮询
2025/08/25 09:10:46.399 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:46.399 ALC:后台服务关闭
2025/08/25 09:10:46.400 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:47.399 ALC:获取数据服务停止
2025/08/25 09:10:54.400 ALC:后台服务启动成功
2025/08/25 09:10:54.411 ALC:自动重启服务成功
2025/08/25 09:10:54.412 ALC:启动自动报警监控
2025/08/25 09:10:54.413 ALC:自动报警监控被关闭
2025/08/25 09:10:54.413 ALC:启动获取数据服务
2025/08/25 09:10:56.411 ALC:1#PLC点位开始轮询
2025/08/25 09:10:56.411 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:56.412 ALC:后台服务关闭
2025/08/25 09:10:56.412 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:56.443 ALC:获取数据服务停止
2025/08/25 09:11:04.428 ALC:后台服务启动成功
2025/08/25 09:11:04.439 ALC:自动重启服务成功
2025/08/25 09:11:04.440 ALC:启动自动报警监控
2025/08/25 09:11:04.440 ALC:自动报警监控被关闭
2025/08/25 09:11:04.441 ALC:启动获取数据服务
2025/08/25 09:11:06.434 ALC:1#PLC点位开始轮询
2025/08/25 09:11:06.435 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:06.435 ALC:后台服务关闭
2025/08/25 09:11:06.436 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:06.466 ALC:获取数据服务停止
2025/08/25 09:11:14.444 ALC:后台服务启动成功
2025/08/25 09:11:14.455 ALC:自动重启服务成功
2025/08/25 09:11:14.456 ALC:启动自动报警监控
2025/08/25 09:11:14.456 ALC:自动报警监控被关闭
2025/08/25 09:11:14.456 ALC:启动获取数据服务
2025/08/25 09:11:16.451 ALC:1#PLC点位开始轮询
2025/08/25 09:11:16.452 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:16.452 ALC:后台服务关闭
2025/08/25 09:11:16.453 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:16.470 ALC:获取数据服务停止
2025/08/25 09:11:24.470 ALC:后台服务启动成功
2025/08/25 09:11:24.479 ALC:启动自动报警监控
2025/08/25 09:11:24.511 ALC:自动报警监控被关闭
2025/08/25 09:11:24.518 ALC:自动重启服务成功
2025/08/25 09:11:24.518 ALC:启动获取数据服务
2025/08/25 09:11:26.488 ALC:1#PLC点位开始轮询
2025/08/25 09:11:26.489 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:26.489 ALC:后台服务关闭
2025/08/25 09:11:26.489 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:26.533 ALC:获取数据服务停止
2025/08/25 09:11:34.535 ALC:后台服务启动成功
2025/08/25 09:11:34.544 ALC:启动自动报警监控
2025/08/25 09:11:34.546 ALC:自动报警监控被关闭
2025/08/25 09:11:34.546 ALC:自动重启服务成功
2025/08/25 09:11:34.547 ALC:启动获取数据服务
2025/08/25 09:11:36.542 ALC:1#PLC点位开始轮询
2025/08/25 09:11:36.543 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:36.544 ALC:后台服务关闭
2025/08/25 09:11:36.544 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:36.573 ALC:获取数据服务停止
2025/08/25 09:11:44.559 ALC:后台服务启动成功
2025/08/25 09:11:44.569 ALC:自动重启服务成功
2025/08/25 09:11:44.569 ALC:启动自动报警监控
2025/08/25 09:11:44.570 ALC:自动报警监控被关闭
2025/08/25 09:11:44.570 ALC:启动获取数据服务
2025/08/25 09:11:46.568 ALC:1#PLC点位开始轮询
2025/08/25 09:11:46.569 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:46.569 ALC:后台服务关闭
2025/08/25 09:11:46.570 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:46.575 ALC:获取数据服务停止
2025/08/25 09:11:54.586 ALC:后台服务启动成功
2025/08/25 09:11:54.596 ALC:自动重启服务成功
2025/08/25 09:11:54.596 ALC:启动自动报警监控
2025/08/25 09:11:54.597 ALC:自动报警监控被关闭
2025/08/25 09:11:54.597 ALC:启动获取数据服务
2025/08/25 09:11:56.605 ALC:1#PLC点位开始轮询
2025/08/25 09:11:56.605 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:11:56.606 ALC:后台服务关闭
2025/08/25 09:11:56.606 ALC:1#PLC点位轮询被关闭
2025/08/25 09:11:56.609 ALC:获取数据服务停止
2025/08/25 09:12:04.600 ALC:后台服务启动成功
2025/08/25 09:12:04.608 ALC:启动自动报警监控
2025/08/25 09:12:04.610 ALC:自动重启服务成功
2025/08/25 09:12:04.611 ALC:自动报警监控被关闭
2025/08/25 09:12:04.611 ALC:启动获取数据服务
2025/08/25 09:12:06.609 ALC:1#PLC点位开始轮询
2025/08/25 09:12:06.610 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:06.610 ALC:后台服务关闭
2025/08/25 09:12:06.611 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:06.617 ALC:获取数据服务停止
2025/08/25 09:12:14.621 ALC:后台服务启动成功
2025/08/25 09:12:14.629 ALC:自动重启服务成功
2025/08/25 09:12:14.629 ALC:启动自动报警监控
2025/08/25 09:12:14.629 ALC:启动获取数据服务
2025/08/25 09:12:14.630 ALC:自动报警监控被关闭
2025/08/25 09:12:16.634 ALC:1#PLC点位开始轮询
2025/08/25 09:12:16.635 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:16.636 ALC:后台服务关闭
2025/08/25 09:12:16.636 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:16.639 ALC:获取数据服务停止
2025/08/25 09:12:24.641 ALC:后台服务启动成功
2025/08/25 09:12:24.651 ALC:启动自动报警监控
2025/08/25 09:12:24.655 ALC:自动报警监控被关闭
2025/08/25 09:12:24.655 ALC:自动重启服务成功
2025/08/25 09:12:24.656 ALC:启动获取数据服务
2025/08/25 09:12:26.662 ALC:1#PLC点位开始轮询
2025/08/25 09:12:26.663 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:26.663 ALC:后台服务关闭
2025/08/25 09:12:26.664 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:26.667 ALC:获取数据服务停止
2025/08/25 09:12:34.670 ALC:后台服务启动成功
2025/08/25 09:12:34.679 ALC:启动自动报警监控
2025/08/25 09:12:34.681 ALC:自动报警监控被关闭
2025/08/25 09:12:34.682 ALC:自动重启服务成功
2025/08/25 09:12:34.683 ALC:启动获取数据服务
2025/08/25 09:12:36.681 ALC:1#PLC点位开始轮询
2025/08/25 09:12:36.682 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:36.684 ALC:后台服务关闭
2025/08/25 09:12:36.684 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:36.696 ALC:获取数据服务停止
2025/08/25 09:12:44.687 ALC:后台服务启动成功
2025/08/25 09:12:44.697 ALC:自动重启服务成功
2025/08/25 09:12:44.697 ALC:启动自动报警监控
2025/08/25 09:12:44.698 ALC:自动报警监控被关闭
2025/08/25 09:12:44.699 ALC:启动获取数据服务
2025/08/25 09:12:46.703 ALC:1#PLC点位开始轮询
2025/08/25 09:12:46.704 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:46.705 ALC:后台服务关闭
2025/08/25 09:12:46.708 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:46.717 ALC:获取数据服务停止
2025/08/25 09:12:54.711 ALC:后台服务启动成功
2025/08/25 09:12:54.725 ALC:自动重启服务成功
2025/08/25 09:12:54.725 ALC:启动自动报警监控
2025/08/25 09:12:54.726 ALC:自动报警监控被关闭
2025/08/25 09:12:54.726 ALC:启动获取数据服务
2025/08/25 09:12:56.738 ALC:1#PLC点位开始轮询
2025/08/25 09:12:56.738 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:12:56.739 ALC:后台服务关闭
2025/08/25 09:12:56.739 ALC:1#PLC点位轮询被关闭
2025/08/25 09:12:56.743 ALC:获取数据服务停止
2025/08/25 09:13:04.740 ALC:后台服务启动成功
2025/08/25 09:13:04.751 ALC:自动重启服务成功
2025/08/25 09:13:04.751 ALC:启动自动报警监控
2025/08/25 09:13:04.752 ALC:自动报警监控被关闭
2025/08/25 09:13:04.753 ALC:启动获取数据服务
2025/08/25 09:13:06.767 ALC:1#PLC点位开始轮询
2025/08/25 09:13:06.767 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:06.768 ALC:后台服务关闭
2025/08/25 09:13:06.769 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:07.767 ALC:获取数据服务停止
2025/08/25 09:13:14.763 ALC:后台服务启动成功
2025/08/25 09:13:14.781 ALC:自动重启服务成功
2025/08/25 09:13:14.782 ALC:启动自动报警监控
2025/08/25 09:13:14.783 ALC:自动报警监控被关闭
2025/08/25 09:13:14.783 ALC:启动获取数据服务
2025/08/25 09:13:16.773 ALC:1#PLC点位开始轮询
2025/08/25 09:13:16.774 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:16.774 ALC:后台服务关闭
2025/08/25 09:13:16.774 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:16.791 ALC:获取数据服务停止
2025/08/25 09:13:24.791 ALC:后台服务启动成功
2025/08/25 09:13:24.807 ALC:自动重启服务成功
2025/08/25 09:13:24.807 ALC:启动自动报警监控
2025/08/25 09:13:24.808 ALC:自动报警监控被关闭
2025/08/25 09:13:24.809 ALC:启动获取数据服务
2025/08/25 09:13:26.803 ALC:1#PLC点位开始轮询
2025/08/25 09:13:26.804 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:26.804 ALC:后台服务关闭
2025/08/25 09:13:26.805 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:26.835 ALC:获取数据服务停止
2025/08/25 09:13:34.812 ALC:后台服务启动成功
2025/08/25 09:13:34.826 ALC:自动重启服务成功
2025/08/25 09:13:34.827 ALC:启动自动报警监控
2025/08/25 09:13:34.827 ALC:自动报警监控被关闭
2025/08/25 09:13:34.829 ALC:启动获取数据服务
2025/08/25 09:13:36.826 ALC:1#PLC点位开始轮询
2025/08/25 09:13:36.826 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:36.827 ALC:后台服务关闭
2025/08/25 09:13:36.828 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:36.834 ALC:获取数据服务停止
2025/08/25 09:13:44.837 ALC:后台服务启动成功
2025/08/25 09:13:44.851 ALC:自动重启服务成功
2025/08/25 09:13:44.852 ALC:启动自动报警监控
2025/08/25 09:13:44.853 ALC:自动报警监控被关闭
2025/08/25 09:13:44.854 ALC:启动获取数据服务
2025/08/25 09:13:46.847 ALC:1#PLC点位开始轮询
2025/08/25 09:13:46.847 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:46.848 ALC:后台服务关闭
2025/08/25 09:13:46.849 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:46.865 ALC:获取数据服务停止
2025/08/25 09:13:54.862 ALC:后台服务启动成功
2025/08/25 09:13:54.873 ALC:启动自动报警监控
2025/08/25 09:13:54.876 ALC:自动重启服务成功
2025/08/25 09:13:54.877 ALC:自动报警监控被关闭
2025/08/25 09:13:54.878 ALC:启动获取数据服务
2025/08/25 09:13:56.875 ALC:1#PLC点位开始轮询
2025/08/25 09:13:56.875 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:13:56.876 ALC:后台服务关闭
2025/08/25 09:13:56.877 ALC:1#PLC点位轮询被关闭
2025/08/25 09:13:56.884 ALC:获取数据服务停止
2025/08/25 09:14:04.894 ALC:后台服务启动成功
2025/08/25 09:14:04.923 ALC:启动自动报警监控
2025/08/25 09:14:04.965 ALC:自动报警监控被关闭
2025/08/25 09:14:04.966 ALC:自动重启服务成功
2025/08/25 09:14:04.968 ALC:启动获取数据服务
2025/08/25 09:14:06.925 ALC:1#PLC点位开始轮询
2025/08/25 09:14:06.926 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:06.927 ALC:后台服务关闭
2025/08/25 09:14:06.928 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:06.975 ALC:获取数据服务停止
2025/08/25 09:14:14.973 ALC:后台服务启动成功
2025/08/25 09:14:14.989 ALC:自动重启服务成功
2025/08/25 09:14:14.990 ALC:启动自动报警监控
2025/08/25 09:14:14.990 ALC:自动报警监控被关闭
2025/08/25 09:14:14.991 ALC:启动获取数据服务
2025/08/25 09:14:16.983 ALC:1#PLC点位开始轮询
2025/08/25 09:14:16.983 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:16.984 ALC:后台服务关闭
2025/08/25 09:14:16.985 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:17.014 ALC:获取数据服务停止
2025/08/25 09:14:24.996 ALC:后台服务启动成功
2025/08/25 09:14:25.026 ALC:自动重启服务成功
2025/08/25 09:14:25.027 ALC:启动自动报警监控
2025/08/25 09:14:25.029 ALC:自动报警监控被关闭
2025/08/25 09:14:25.030 ALC:启动获取数据服务
2025/08/25 09:14:27.018 ALC:1#PLC点位开始轮询
2025/08/25 09:14:27.020 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:27.021 ALC:后台服务关闭
2025/08/25 09:14:27.022 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:27.049 ALC:获取数据服务停止
2025/08/25 09:14:35.036 ALC:后台服务启动成功
2025/08/25 09:14:35.062 ALC:自动重启服务成功
2025/08/25 09:14:35.063 ALC:启动自动报警监控
2025/08/25 09:14:35.064 ALC:自动报警监控被关闭
2025/08/25 09:14:35.065 ALC:启动获取数据服务
2025/08/25 09:14:37.056 ALC:1#PLC点位开始轮询
2025/08/25 09:14:37.057 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:37.057 ALC:后台服务关闭
2025/08/25 09:14:37.059 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:37.085 ALC:获取数据服务停止
2025/08/25 09:14:45.081 ALC:后台服务启动成功
2025/08/25 09:14:45.096 ALC:启动自动报警监控
2025/08/25 09:14:45.098 ALC:自动报警监控被关闭
2025/08/25 09:14:45.102 ALC:自动重启服务成功
2025/08/25 09:14:45.103 ALC:启动获取数据服务
2025/08/25 09:14:47.101 ALC:1#PLC点位开始轮询
2025/08/25 09:14:47.102 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:47.102 ALC:后台服务关闭
2025/08/25 09:14:47.103 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:47.114 ALC:获取数据服务停止
2025/08/25 09:14:55.108 ALC:后台服务启动成功
2025/08/25 09:14:55.125 ALC:自动重启服务成功
2025/08/25 09:14:55.126 ALC:启动自动报警监控
2025/08/25 09:14:55.127 ALC:自动报警监控被关闭
2025/08/25 09:14:55.128 ALC:启动获取数据服务
2025/08/25 09:14:57.122 ALC:1#PLC点位开始轮询
2025/08/25 09:14:57.123 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:14:57.124 ALC:后台服务关闭
2025/08/25 09:14:57.125 ALC:1#PLC点位轮询被关闭
2025/08/25 09:14:57.153 ALC:获取数据服务停止
2025/08/25 09:15:05.134 ALC:后台服务启动成功
2025/08/25 09:15:05.150 ALC:启动自动报警监控
2025/08/25 09:15:05.152 ALC:自动报警监控被关闭
2025/08/25 09:15:05.157 ALC:自动重启服务成功
2025/08/25 09:15:05.157 ALC:启动获取数据服务
2025/08/25 09:15:07.149 ALC:1#PLC点位开始轮询
2025/08/25 09:15:07.151 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:07.151 ALC:后台服务关闭
2025/08/25 09:15:07.152 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:07.165 ALC:获取数据服务停止
2025/08/25 09:15:15.252 ALC:后台服务启动成功
2025/08/25 09:15:15.544 ALC:启动自动报警监控
2025/08/25 09:15:15.559 ALC:自动报警监控被关闭
2025/08/25 09:15:15.572 ALC:自动重启服务成功
2025/08/25 09:15:15.629 ALC:启动获取数据服务
2025/08/25 09:15:17.504 ALC:1#PLC点位开始轮询
2025/08/25 09:15:17.506 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:17.509 ALC:后台服务关闭
2025/08/25 09:15:17.517 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:17.661 ALC:获取数据服务停止
2025/08/25 09:15:25.686 ALC:后台服务启动成功
2025/08/25 09:15:25.707 ALC:自动重启服务成功
2025/08/25 09:15:25.712 ALC:启动自动报警监控
2025/08/25 09:15:25.713 ALC:自动报警监控被关闭
2025/08/25 09:15:25.714 ALC:启动获取数据服务
2025/08/25 09:15:27.703 ALC:1#PLC点位开始轮询
2025/08/25 09:15:27.704 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:27.705 ALC:后台服务关闭
2025/08/25 09:15:27.706 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:27.734 ALC:获取数据服务停止
2025/08/25 09:15:35.722 ALC:后台服务启动成功
2025/08/25 09:15:35.737 ALC:启动自动报警监控
2025/08/25 09:15:35.762 ALC:自动报警监控被关闭
2025/08/25 09:15:35.763 ALC:自动重启服务成功
2025/08/25 09:15:35.765 ALC:启动获取数据服务
2025/08/25 09:15:37.738 ALC:1#PLC点位开始轮询
2025/08/25 09:15:37.739 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:37.739 ALC:后台服务关闭
2025/08/25 09:15:37.740 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:37.785 ALC:获取数据服务停止
2025/08/25 09:15:45.781 ALC:后台服务启动成功
2025/08/25 09:15:45.836 ALC:启动自动报警监控
2025/08/25 09:15:45.843 ALC:自动报警监控被关闭
2025/08/25 09:15:45.844 ALC:自动重启服务成功
2025/08/25 09:15:45.845 ALC:启动获取数据服务
2025/08/25 09:15:47.833 ALC:1#PLC点位开始轮询
2025/08/25 09:15:47.834 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:47.835 ALC:后台服务关闭
2025/08/25 09:15:47.835 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:47.855 ALC:获取数据服务停止
2025/08/25 09:15:55.859 ALC:后台服务启动成功
2025/08/25 09:15:55.877 ALC:自动重启服务成功
2025/08/25 09:15:55.877 ALC:启动自动报警监控
2025/08/25 09:15:55.878 ALC:自动报警监控被关闭
2025/08/25 09:15:55.879 ALC:启动获取数据服务
2025/08/25 09:15:57.882 ALC:1#PLC点位开始轮询
2025/08/25 09:15:57.883 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:15:57.886 ALC:后台服务关闭
2025/08/25 09:15:57.887 ALC:1#PLC点位轮询被关闭
2025/08/25 09:15:57.891 ALC:获取数据服务停止
2025/08/25 09:16:05.899 ALC:后台服务启动成功
2025/08/25 09:16:05.920 ALC:启动自动报警监控
2025/08/25 09:16:05.921 ALC:自动报警监控被关闭
2025/08/25 09:16:05.922 ALC:启动获取数据服务
2025/08/25 09:16:05.923 ALC:自动重启服务成功
2025/08/25 09:16:07.920 ALC:1#PLC点位开始轮询
2025/08/25 09:16:07.923 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:07.924 ALC:后台服务关闭
2025/08/25 09:16:07.925 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:07.964 ALC:获取数据服务停止
2025/08/25 09:16:16.007 ALC:后台服务启动成功
2025/08/25 09:16:16.768 ALC:启动自动报警监控
2025/08/25 09:16:16.962 ALC:自动报警监控被关闭
2025/08/25 09:16:17.020 ALC:自动重启服务成功
2025/08/25 09:16:17.028 ALC:启动获取数据服务
2025/08/25 09:16:18.478 ALC:1#PLC点位开始轮询
2025/08/25 09:16:18.480 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:18.481 ALC:后台服务关闭
2025/08/25 09:16:18.482 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:19.092 ALC:获取数据服务停止
2025/08/25 09:16:27.246 ALC:后台服务启动成功
2025/08/25 09:16:27.280 ALC:启动自动报警监控
2025/08/25 09:16:27.280 ALC:自动报警监控被关闭
2025/08/25 09:16:27.292 ALC:自动重启服务成功
2025/08/25 09:16:27.293 ALC:启动获取数据服务
2025/08/25 09:16:29.270 ALC:1#PLC点位开始轮询
2025/08/25 09:16:29.271 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:29.271 ALC:后台服务关闭
2025/08/25 09:16:29.273 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:29.316 ALC:获取数据服务停止
2025/08/25 09:16:37.307 ALC:后台服务启动成功
2025/08/25 09:16:37.364 ALC:自动重启服务成功
2025/08/25 09:16:37.365 ALC:启动自动报警监控
2025/08/25 09:16:37.366 ALC:自动报警监控被关闭
2025/08/25 09:16:37.367 ALC:启动获取数据服务
2025/08/25 09:16:39.360 ALC:1#PLC点位开始轮询
2025/08/25 09:16:39.362 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:39.364 ALC:后台服务关闭
2025/08/25 09:16:39.368 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:39.389 ALC:获取数据服务停止
2025/08/25 09:16:47.373 ALC:后台服务启动成功
2025/08/25 09:16:47.395 ALC:自动重启服务成功
2025/08/25 09:16:47.395 ALC:启动自动报警监控
2025/08/25 09:16:47.396 ALC:自动报警监控被关闭
2025/08/25 09:16:47.397 ALC:启动获取数据服务
2025/08/25 09:16:49.392 ALC:1#PLC点位开始轮询
2025/08/25 09:16:49.394 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:49.395 ALC:后台服务关闭
2025/08/25 09:16:49.398 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:49.409 ALC:获取数据服务停止
2025/08/25 09:16:57.409 ALC:后台服务启动成功
2025/08/25 09:16:57.423 ALC:自动重启服务成功
2025/08/25 09:16:57.424 ALC:启动自动报警监控
2025/08/25 09:16:57.424 ALC:自动报警监控被关闭
2025/08/25 09:16:57.425 ALC:启动获取数据服务
2025/08/25 09:16:59.428 ALC:1#PLC点位开始轮询
2025/08/25 09:16:59.429 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:16:59.430 ALC:后台服务关闭
2025/08/25 09:16:59.430 ALC:1#PLC点位轮询被关闭
2025/08/25 09:16:59.441 ALC:获取数据服务停止
2025/08/25 09:17:07.439 ALC:后台服务启动成功
2025/08/25 09:17:07.452 ALC:自动重启服务成功
2025/08/25 09:17:07.453 ALC:启动自动报警监控
2025/08/25 09:17:07.453 ALC:自动报警监控被关闭
2025/08/25 09:17:07.454 ALC:启动获取数据服务
2025/08/25 09:17:09.460 ALC:1#PLC点位开始轮询
2025/08/25 09:17:09.464 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:09.464 ALC:后台服务关闭
2025/08/25 09:17:09.465 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:10.473 ALC:获取数据服务停止
2025/08/25 09:17:17.478 ALC:后台服务启动成功
2025/08/25 09:17:17.495 ALC:启动自动报警监控
2025/08/25 09:17:17.500 ALC:自动报警监控被关闭
2025/08/25 09:17:17.501 ALC:启动获取数据服务
2025/08/25 09:17:17.502 ALC:自动重启服务成功
2025/08/25 09:17:19.499 ALC:1#PLC点位开始轮询
2025/08/25 09:17:19.499 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:19.500 ALC:后台服务关闭
2025/08/25 09:17:19.501 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:19.530 ALC:获取数据服务停止
2025/08/25 09:17:27.521 ALC:后台服务启动成功
2025/08/25 09:17:27.543 ALC:自动重启服务成功
2025/08/25 09:17:27.543 ALC:启动自动报警监控
2025/08/25 09:17:27.544 ALC:自动报警监控被关闭
2025/08/25 09:17:27.545 ALC:启动获取数据服务
2025/08/25 09:17:29.544 ALC:1#PLC点位开始轮询
2025/08/25 09:17:29.545 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:29.545 ALC:后台服务关闭
2025/08/25 09:17:29.546 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:29.553 ALC:获取数据服务停止
2025/08/25 09:17:37.556 ALC:后台服务启动成功
2025/08/25 09:17:37.572 ALC:启动自动报警监控
2025/08/25 09:17:37.573 ALC:自动报警监控被关闭
2025/08/25 09:17:37.574 ALC:启动获取数据服务
2025/08/25 09:17:37.575 ALC:自动重启服务成功
2025/08/25 09:17:39.577 ALC:1#PLC点位开始轮询
2025/08/25 09:17:39.578 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:39.582 ALC:后台服务关闭
2025/08/25 09:17:39.582 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:39.584 ALC:获取数据服务停止
2025/08/25 09:17:47.581 ALC:后台服务启动成功
2025/08/25 09:17:47.597 ALC:自动重启服务成功
2025/08/25 09:17:47.598 ALC:启动自动报警监控
2025/08/25 09:17:47.598 ALC:自动报警监控被关闭
2025/08/25 09:17:47.599 ALC:启动获取数据服务
2025/08/25 09:17:49.603 ALC:1#PLC点位开始轮询
2025/08/25 09:17:49.609 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:49.610 ALC:后台服务关闭
2025/08/25 09:17:49.611 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:50.617 ALC:获取数据服务停止
2025/08/25 09:17:57.602 ALC:后台服务启动成功
2025/08/25 09:17:57.624 ALC:启动自动报警监控
2025/08/25 09:17:57.656 ALC:自动报警监控被关闭
2025/08/25 09:17:57.657 ALC:自动重启服务成功
2025/08/25 09:17:57.658 ALC:启动获取数据服务
2025/08/25 09:17:59.626 ALC:1#PLC点位开始轮询
2025/08/25 09:17:59.626 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:17:59.627 ALC:后台服务关闭
2025/08/25 09:17:59.628 ALC:1#PLC点位轮询被关闭
2025/08/25 09:17:59.675 ALC:获取数据服务停止
2025/08/25 09:18:07.675 ALC:后台服务启动成功
2025/08/25 09:18:07.699 ALC:启动自动报警监控
2025/08/25 09:18:07.701 ALC:自动报警监控被关闭
2025/08/25 09:18:07.707 ALC:自动重启服务成功
2025/08/25 09:18:07.707 ALC:启动获取数据服务
2025/08/25 09:18:09.700 ALC:1#PLC点位开始轮询
2025/08/25 09:18:09.701 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:18:09.702 ALC:后台服务关闭
2025/08/25 09:18:09.703 ALC:1#PLC点位轮询被关闭
2025/08/25 09:18:09.731 ALC:获取数据服务停止
2025/08/25 09:18:17.712 ALC:后台服务启动成功
2025/08/25 09:18:17.729 ALC:自动重启服务成功
2025/08/25 09:18:17.729 ALC:启动自动报警监控
2025/08/25 09:18:17.730 ALC:自动报警监控被关闭
2025/08/25 09:18:17.731 ALC:启动获取数据服务
2025/08/25 09:18:19.726 ALC:1#PLC点位开始轮询
2025/08/25 09:18:19.727 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:18:19.728 ALC:后台服务关闭
2025/08/25 09:18:19.729 ALC:1#PLC点位轮询被关闭
2025/08/25 09:18:19.742 ALC:获取数据服务停止
2025/08/25 09:18:27.738 ALC:后台服务启动成功
2025/08/25 09:18:27.750 ALC:自动重启服务成功
2025/08/25 09:18:27.752 ALC:启动自动报警监控
2025/08/25 09:18:27.754 ALC:自动报警监控被关闭
2025/08/25 09:18:27.755 ALC:启动获取数据服务
2025/08/25 09:18:29.756 ALC:1#PLC点位开始轮询
2025/08/25 09:18:29.758 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:18:29.759 ALC:后台服务关闭
2025/08/25 09:18:29.759 ALC:1#PLC点位轮询被关闭
2025/08/25 09:18:29.770 ALC:获取数据服务停止
2025/08/25 09:18:37.779 ALC:后台服务启动成功
2025/08/25 09:18:37.797 ALC:自动重启服务成功
2025/08/25 09:18:37.799 ALC:启动自动报警监控
2025/08/25 09:18:37.800 ALC:自动报警监控被关闭
2025/08/25 09:18:37.801 ALC:启动获取数据服务
2025/08/25 09:18:39.805 ALC:1#PLC点位开始轮询
2025/08/25 09:18:39.806 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:18:39.807 ALC:后台服务关闭
2025/08/25 09:18:39.807 ALC:1#PLC点位轮询被关闭
2025/08/25 09:18:39.818 ALC:获取数据服务停止
2025/08/25 09:18:44.833 ALC:自动重启服务失败。System.Exception: 连接************:502超时2s
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 112
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:18:54.840 ALC:后台服务启动成功
2025/08/25 09:18:54.854 ALC:启动自动报警监控
2025/08/25 09:18:54.889 ALC:自动报警监控被关闭
2025/08/25 09:18:54.890 ALC:自动重启服务成功
2025/08/25 09:18:54.891 ALC:启动获取数据服务
2025/08/25 09:18:56.857 ALC:1#PLC点位开始轮询
2025/08/25 09:18:56.858 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:18:56.859 ALC:后台服务关闭
2025/08/25 09:18:56.860 ALC:1#PLC点位轮询被关闭
2025/08/25 09:18:56.908 ALC:获取数据服务停止
2025/08/25 09:19:04.948 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 你的主机中的软件中止了一个已建立的连接。.
 ---> System.Net.Sockets.SocketException (10053): 你的主机中的软件中止了一个已建立的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 124
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:19:14.970 ALC:后台服务启动成功
2025/08/25 09:19:14.983 ALC:自动重启服务成功
2025/08/25 09:19:14.985 ALC:启动自动报警监控
2025/08/25 09:19:14.985 ALC:自动报警监控被关闭
2025/08/25 09:19:14.986 ALC:启动获取数据服务
2025/08/25 09:19:16.978 ALC:1#PLC点位开始轮询
2025/08/25 09:19:16.979 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:19:16.979 ALC:后台服务关闭
2025/08/25 09:19:16.980 ALC:1#PLC点位轮询被关闭
2025/08/25 09:19:16.994 ALC:获取数据服务停止
2025/08/25 09:19:24.991 ALC:后台服务启动成功
2025/08/25 09:19:25.002 ALC:自动重启服务成功
2025/08/25 09:19:25.002 ALC:启动自动报警监控
2025/08/25 09:19:25.003 ALC:自动报警监控被关闭
2025/08/25 09:19:25.003 ALC:启动获取数据服务
2025/08/25 09:19:27.008 ALC:1#PLC点位开始轮询
2025/08/25 09:19:27.009 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:19:27.010 ALC:后台服务关闭
2025/08/25 09:19:27.010 ALC:1#PLC点位轮询被关闭
2025/08/25 09:19:28.020 ALC:获取数据服务停止
2025/08/25 09:19:35.010 ALC:后台服务启动成功
2025/08/25 09:19:35.018 ALC:启动自动报警监控
2025/08/25 09:19:35.045 ALC:自动报警监控被关闭
2025/08/25 09:19:35.046 ALC:自动重启服务成功
2025/08/25 09:19:35.047 ALC:启动获取数据服务
2025/08/25 09:19:37.024 ALC:1#PLC点位开始轮询
2025/08/25 09:19:37.025 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:19:37.025 ALC:后台服务关闭
2025/08/25 09:19:37.025 ALC:1#PLC点位轮询被关闭
2025/08/25 09:19:37.070 ALC:获取数据服务停止
2025/08/25 09:19:45.063 ALC:后台服务启动成功
2025/08/25 09:19:45.070 ALC:自动重启服务成功
2025/08/25 09:19:45.071 ALC:启动自动报警监控
2025/08/25 09:19:45.071 ALC:自动报警监控被关闭
2025/08/25 09:19:45.072 ALC:启动获取数据服务
2025/08/25 09:19:47.077 ALC:1#PLC点位开始轮询
2025/08/25 09:19:47.078 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:19:47.078 ALC:后台服务关闭
2025/08/25 09:19:47.079 ALC:1#PLC点位轮询被关闭
2025/08/25 09:19:47.090 ALC:获取数据服务停止
2025/08/25 09:19:50.259 Executed endpoint '/socket'. 
2025/08/25 09:19:50.261 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=6oPfVr5eLZJbxQ2Pz61TGA - - - 101 - - 722398.6571ms. 
2025/08/25 09:19:51.807 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:19:51.812 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 5.3273ms. 
2025/08/25 09:19:51.993 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:19:51.993 CORS policy execution successful. 
2025/08/25 09:19:51.996 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:19:52.012 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:19:52.025 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:19:52.026 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 32.6486ms. 
2025/08/25 09:19:52.029 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:19:52.030 CORS policy execution successful. 
2025/08/25 09:19:52.030 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.1622ms. 
2025/08/25 09:19:52.041 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:19:52.042 CORS policy execution successful. 
2025/08/25 09:19:52.042 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:19:52.042 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:19:52.043 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.8080ms. 
2025/08/25 09:19:52.138 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=0jHwgf25PYZn-NIcWrgXOA - -. 
2025/08/25 09:19:52.210 CORS policy execution successful. 
2025/08/25 09:19:52.211 Executing endpoint '/socket'. 
2025/08/25 09:19:55.128 ALC:后台服务启动成功
2025/08/25 09:19:55.140 ALC:自动重启服务成功
2025/08/25 09:19:55.140 ALC:启动自动报警监控
2025/08/25 09:19:55.141 ALC:自动报警监控被关闭
2025/08/25 09:19:55.141 ALC:启动获取数据服务
2025/08/25 09:19:57.138 ALC:1#PLC点位开始轮询
2025/08/25 09:19:57.139 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:19:57.140 ALC:后台服务关闭
2025/08/25 09:19:57.140 ALC:1#PLC点位轮询被关闭
2025/08/25 09:19:57.154 ALC:获取数据服务停止
2025/08/25 09:20:05.156 ALC:后台服务启动成功
2025/08/25 09:20:05.165 ALC:自动重启服务成功
2025/08/25 09:20:05.166 ALC:启动获取数据服务
2025/08/25 09:20:05.166 ALC:启动自动报警监控
2025/08/25 09:20:05.167 ALC:自动报警监控被关闭
2025/08/25 09:20:07.178 ALC:1#PLC点位开始轮询
2025/08/25 09:20:07.181 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:07.181 ALC:后台服务关闭
2025/08/25 09:20:07.182 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:08.193 ALC:获取数据服务停止
2025/08/25 09:20:15.182 ALC:后台服务启动成功
2025/08/25 09:20:15.192 ALC:自动重启服务成功
2025/08/25 09:20:15.193 ALC:启动自动报警监控
2025/08/25 09:20:15.193 ALC:自动报警监控被关闭
2025/08/25 09:20:15.193 ALC:启动获取数据服务
2025/08/25 09:20:17.200 ALC:1#PLC点位开始轮询
2025/08/25 09:20:17.201 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:17.202 ALC:后台服务关闭
2025/08/25 09:20:17.202 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:18.212 ALC:获取数据服务停止
2025/08/25 09:20:25.205 ALC:后台服务启动成功
2025/08/25 09:20:25.215 ALC:启动自动报警监控
2025/08/25 09:20:25.241 ALC:自动报警监控被关闭
2025/08/25 09:20:25.242 ALC:自动重启服务成功
2025/08/25 09:20:25.243 ALC:启动获取数据服务
2025/08/25 09:20:27.228 ALC:1#PLC点位开始轮询
2025/08/25 09:20:27.229 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:27.229 ALC:后台服务关闭
2025/08/25 09:20:27.230 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:27.260 ALC:获取数据服务停止
2025/08/25 09:20:35.250 ALC:后台服务启动成功
2025/08/25 09:20:35.267 ALC:自动重启服务成功
2025/08/25 09:20:35.277 ALC:启动自动报警监控
2025/08/25 09:20:35.280 ALC:自动报警监控被关闭
2025/08/25 09:20:35.283 ALC:启动获取数据服务
2025/08/25 09:20:37.264 ALC:1#PLC点位开始轮询
2025/08/25 09:20:37.265 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:37.266 ALC:后台服务关闭
2025/08/25 09:20:37.267 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:37.311 ALC:获取数据服务停止
2025/08/25 09:20:45.288 ALC:后台服务启动成功
2025/08/25 09:20:45.296 ALC:启动自动报警监控
2025/08/25 09:20:45.301 ALC:自动报警监控被关闭
2025/08/25 09:20:45.301 ALC:自动重启服务成功
2025/08/25 09:20:45.302 ALC:启动获取数据服务
2025/08/25 09:20:47.306 ALC:1#PLC点位开始轮询
2025/08/25 09:20:47.306 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:47.307 ALC:后台服务关闭
2025/08/25 09:20:47.307 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:47.310 ALC:获取数据服务停止
2025/08/25 09:20:55.337 ALC:后台服务启动成功
2025/08/25 09:20:55.382 ALC:自动重启服务成功
2025/08/25 09:20:55.397 ALC:启动自动报警监控
2025/08/25 09:20:55.398 ALC:自动报警监控被关闭
2025/08/25 09:20:55.400 ALC:启动获取数据服务
2025/08/25 09:20:57.386 ALC:1#PLC点位开始轮询
2025/08/25 09:20:57.387 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:20:57.388 ALC:后台服务关闭
2025/08/25 09:20:57.388 ALC:1#PLC点位轮询被关闭
2025/08/25 09:20:57.418 ALC:获取数据服务停止
2025/08/25 09:21:05.414 ALC:后台服务启动成功
2025/08/25 09:21:05.424 ALC:自动重启服务成功
2025/08/25 09:21:05.424 ALC:启动自动报警监控
2025/08/25 09:21:05.425 ALC:自动报警监控被关闭
2025/08/25 09:21:05.425 ALC:启动获取数据服务
2025/08/25 09:21:07.430 ALC:1#PLC点位开始轮询
2025/08/25 09:21:07.430 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:07.431 ALC:后台服务关闭
2025/08/25 09:21:07.431 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:07.442 ALC:获取数据服务停止
2025/08/25 09:21:15.441 ALC:后台服务启动成功
2025/08/25 09:21:15.453 ALC:自动重启服务成功
2025/08/25 09:21:15.454 ALC:启动自动报警监控
2025/08/25 09:21:15.454 ALC:自动报警监控被关闭
2025/08/25 09:21:15.455 ALC:启动获取数据服务
2025/08/25 09:21:17.461 ALC:1#PLC点位开始轮询
2025/08/25 09:21:17.462 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:17.462 ALC:后台服务关闭
2025/08/25 09:21:17.463 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:17.466 ALC:获取数据服务停止
2025/08/25 09:21:25.465 ALC:后台服务启动成功
2025/08/25 09:21:25.476 ALC:启动自动报警监控
2025/08/25 09:21:25.508 ALC:自动报警监控被关闭
2025/08/25 09:21:25.509 ALC:自动重启服务成功
2025/08/25 09:21:25.510 ALC:启动获取数据服务
2025/08/25 09:21:27.475 ALC:1#PLC点位开始轮询
2025/08/25 09:21:27.476 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:27.477 ALC:后台服务关闭
2025/08/25 09:21:27.477 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:27.527 ALC:获取数据服务停止
2025/08/25 09:21:35.522 ALC:后台服务启动成功
2025/08/25 09:21:35.537 ALC:自动重启服务成功
2025/08/25 09:21:35.538 ALC:启动自动报警监控
2025/08/25 09:21:35.539 ALC:自动报警监控被关闭
2025/08/25 09:21:35.540 ALC:启动获取数据服务
2025/08/25 09:21:37.530 ALC:1#PLC点位开始轮询
2025/08/25 09:21:37.531 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:37.531 ALC:后台服务关闭
2025/08/25 09:21:37.531 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:37.562 ALC:获取数据服务停止
2025/08/25 09:21:45.545 ALC:后台服务启动成功
2025/08/25 09:21:45.556 ALC:自动重启服务成功
2025/08/25 09:21:45.557 ALC:启动自动报警监控
2025/08/25 09:21:45.558 ALC:自动报警监控被关闭
2025/08/25 09:21:45.559 ALC:启动获取数据服务
2025/08/25 09:21:47.557 ALC:1#PLC点位开始轮询
2025/08/25 09:21:47.557 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:47.558 ALC:后台服务关闭
2025/08/25 09:21:47.558 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:47.572 ALC:获取数据服务停止
2025/08/25 09:21:55.565 ALC:后台服务启动成功
2025/08/25 09:21:55.575 ALC:自动重启服务成功
2025/08/25 09:21:55.576 ALC:启动自动报警监控
2025/08/25 09:21:55.576 ALC:自动报警监控被关闭
2025/08/25 09:21:55.576 ALC:启动获取数据服务
2025/08/25 09:21:57.589 ALC:1#PLC点位开始轮询
2025/08/25 09:21:57.590 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:21:57.590 ALC:后台服务关闭
2025/08/25 09:21:57.591 ALC:1#PLC点位轮询被关闭
2025/08/25 09:21:58.597 ALC:获取数据服务停止
2025/08/25 09:22:05.593 ALC:后台服务启动成功
2025/08/25 09:22:05.603 ALC:自动重启服务成功
2025/08/25 09:22:05.604 ALC:启动自动报警监控
2025/08/25 09:22:05.604 ALC:自动报警监控被关闭
2025/08/25 09:22:05.604 ALC:启动获取数据服务
2025/08/25 09:22:07.599 ALC:1#PLC点位开始轮询
2025/08/25 09:22:07.600 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:07.600 ALC:后台服务关闭
2025/08/25 09:22:07.600 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:07.615 ALC:获取数据服务停止
2025/08/25 09:22:15.620 ALC:后台服务启动成功
2025/08/25 09:22:15.629 ALC:自动重启服务成功
2025/08/25 09:22:15.630 ALC:启动自动报警监控
2025/08/25 09:22:15.630 ALC:自动报警监控被关闭
2025/08/25 09:22:15.630 ALC:启动获取数据服务
2025/08/25 09:22:17.642 ALC:1#PLC点位开始轮询
2025/08/25 09:22:17.643 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:17.644 ALC:后台服务关闭
2025/08/25 09:22:17.644 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:18.655 ALC:获取数据服务停止
2025/08/25 09:22:20.174 Executed endpoint '/socket'. 
2025/08/25 09:22:20.175 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=0jHwgf25PYZn-NIcWrgXOA - - - 101 - - 148036.7241ms. 
2025/08/25 09:22:21.076 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:22:21.080 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 3.7266ms. 
2025/08/25 09:22:21.223 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:22:21.223 CORS policy execution successful. 
2025/08/25 09:22:21.226 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:22:21.243 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:22:21.243 CORS policy execution successful. 
2025/08/25 09:22:21.244 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.6815ms. 
2025/08/25 09:22:21.244 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:22:21.246 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:22:21.247 CORS policy execution successful. 
2025/08/25 09:22:21.247 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:22:21.248 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:22:21.248 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.1076ms. 
2025/08/25 09:22:21.263 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:22:21.264 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 40.8717ms. 
2025/08/25 09:22:21.312 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=cTUx1cX_WsAwGkGW1xdtOA - -. 
2025/08/25 09:22:21.313 CORS policy execution successful. 
2025/08/25 09:22:21.315 Executing endpoint '/socket'. 
2025/08/25 09:22:25.649 ALC:后台服务启动成功
2025/08/25 09:22:25.658 ALC:启动自动报警监控
2025/08/25 09:22:25.694 ALC:自动报警监控被关闭
2025/08/25 09:22:25.695 ALC:自动重启服务成功
2025/08/25 09:22:25.695 ALC:启动获取数据服务
2025/08/25 09:22:27.664 ALC:1#PLC点位开始轮询
2025/08/25 09:22:27.665 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:27.666 ALC:后台服务关闭
2025/08/25 09:22:27.667 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:27.712 ALC:获取数据服务停止
2025/08/25 09:22:35.713 ALC:后台服务启动成功
2025/08/25 09:22:35.724 ALC:启动自动报警监控
2025/08/25 09:22:35.751 ALC:自动报警监控被关闭
2025/08/25 09:22:35.752 ALC:自动重启服务成功
2025/08/25 09:22:35.752 ALC:启动获取数据服务
2025/08/25 09:22:37.735 ALC:1#PLC点位开始轮询
2025/08/25 09:22:37.736 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:37.737 ALC:后台服务关闭
2025/08/25 09:22:37.737 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:37.801 ALC:获取数据服务停止
2025/08/25 09:22:45.764 ALC:后台服务启动成功
2025/08/25 09:22:45.772 ALC:启动自动报警监控
2025/08/25 09:22:45.808 ALC:自动报警监控被关闭
2025/08/25 09:22:45.809 ALC:自动重启服务成功
2025/08/25 09:22:45.810 ALC:启动获取数据服务
2025/08/25 09:22:47.781 ALC:1#PLC点位开始轮询
2025/08/25 09:22:47.782 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:47.783 ALC:后台服务关闭
2025/08/25 09:22:47.783 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:47.826 ALC:获取数据服务停止
2025/08/25 09:22:55.350 Executed endpoint '/socket'. 
2025/08/25 09:22:55.374 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=cTUx1cX_WsAwGkGW1xdtOA - - - 101 - - 34061.7516ms. 
2025/08/25 09:22:55.825 ALC:后台服务启动成功
2025/08/25 09:22:55.853 ALC:启动自动报警监控
2025/08/25 09:22:55.857 ALC:自动重启服务成功
2025/08/25 09:22:55.858 ALC:自动报警监控被关闭
2025/08/25 09:22:55.858 ALC:启动获取数据服务
2025/08/25 09:22:56.207 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:22:56.208 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.5933ms. 
2025/08/25 09:22:56.280 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:22:56.280 CORS policy execution successful. 
2025/08/25 09:22:56.282 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:22:56.290 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:22:56.291 CORS policy execution successful. 
2025/08/25 09:22:56.292 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.8915ms. 
2025/08/25 09:22:56.296 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:22:56.296 CORS policy execution successful. 
2025/08/25 09:22:56.297 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:22:56.297 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:22:56.297 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:22:56.297 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.4680ms. 
2025/08/25 09:22:56.313 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=wR-vpncYxOazs0Sr_-xv-g - -. 
2025/08/25 09:22:56.313 CORS policy execution successful. 
2025/08/25 09:22:56.313 Executing endpoint '/socket'. 
2025/08/25 09:22:56.314 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:22:56.315 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 34.8617ms. 
2025/08/25 09:22:57.847 ALC:1#PLC点位开始轮询
2025/08/25 09:22:57.849 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:22:57.849 ALC:后台服务关闭
2025/08/25 09:22:57.850 ALC:1#PLC点位轮询被关闭
2025/08/25 09:22:57.895 ALC:获取数据服务停止
2025/08/25 09:23:05.865 ALC:后台服务启动成功
2025/08/25 09:23:05.873 ALC:自动重启服务成功
2025/08/25 09:23:05.874 ALC:启动自动报警监控
2025/08/25 09:23:05.874 ALC:自动报警监控被关闭
2025/08/25 09:23:05.874 ALC:启动获取数据服务
2025/08/25 09:23:07.882 ALC:1#PLC点位开始轮询
2025/08/25 09:23:07.882 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:23:07.883 ALC:后台服务关闭
2025/08/25 09:23:07.884 ALC:1#PLC点位轮询被关闭
2025/08/25 09:23:08.897 ALC:获取数据服务停止
2025/08/25 09:23:15.879 ALC:后台服务启动成功
2025/08/25 09:23:15.890 ALC:自动重启服务成功
2025/08/25 09:23:15.891 ALC:启动自动报警监控
2025/08/25 09:23:15.891 ALC:自动报警监控被关闭
2025/08/25 09:23:15.892 ALC:启动获取数据服务
2025/08/25 09:23:17.885 ALC:1#PLC点位开始轮询
2025/08/25 09:23:17.886 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:23:17.886 ALC:后台服务关闭
2025/08/25 09:23:17.887 ALC:1#PLC点位轮询被关闭
2025/08/25 09:23:17.917 ALC:获取数据服务停止
2025/08/25 09:23:25.911 ALC:后台服务启动成功
2025/08/25 09:23:25.936 ALC:启动自动报警监控
2025/08/25 09:23:25.942 ALC:自动报警监控被关闭
2025/08/25 09:23:25.951 ALC:自动重启服务成功
2025/08/25 09:23:25.952 ALC:启动获取数据服务
2025/08/25 09:23:27.936 ALC:1#PLC点位开始轮询
2025/08/25 09:23:27.937 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:23:27.937 ALC:后台服务关闭
2025/08/25 09:23:27.938 ALC:1#PLC点位轮询被关闭
2025/08/25 09:23:27.966 ALC:获取数据服务停止
2025/08/25 09:23:36.554 ALC:后台服务启动成功
2025/08/25 09:23:37.311 ALC:启动自动报警监控
2025/08/25 09:23:37.977 ALC:自动报警监控被关闭
2025/08/25 09:23:38.066 ALC:自动重启服务成功
2025/08/25 09:23:38.730 ALC:启动获取数据服务
2025/08/25 09:23:39.080 ALC:1#PLC点位开始轮询
2025/08/25 09:23:39.081 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:23:39.081 ALC:后台服务关闭
2025/08/25 09:23:39.082 ALC:1#PLC点位轮询被关闭
2025/08/25 09:23:40.111 ALC:获取数据服务停止
2025/08/25 09:23:46.049 Executed endpoint '/socket'. 
2025/08/25 09:23:46.049 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=wR-vpncYxOazs0Sr_-xv-g - - - 101 - - 49736.5660ms. 
2025/08/25 09:23:48.686 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:23:48.687 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.2557ms. 
2025/08/25 09:23:48.853 ALC:后台服务启动成功
2025/08/25 09:23:48.878 ALC:启动自动报警监控
2025/08/25 09:23:48.880 ALC:自动报警监控被关闭
2025/08/25 09:23:48.884 ALC:自动重启服务成功
2025/08/25 09:23:48.885 ALC:启动获取数据服务
2025/08/25 09:23:48.897 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:23:48.898 CORS policy execution successful. 
2025/08/25 09:23:48.900 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:23:48.932 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:23:48.988 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:23:48.989 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 91.7122ms. 
2025/08/25 09:23:49.073 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:23:49.078 CORS policy execution successful. 
2025/08/25 09:23:49.079 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 5.9861ms. 
2025/08/25 09:23:49.096 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:23:49.109 CORS policy execution successful. 
2025/08/25 09:23:49.115 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:23:49.158 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:23:49.163 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 66.7375ms. 
2025/08/25 09:23:49.444 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=kSymop5Vdm4V-z8jFBJF3w - -. 
2025/08/25 09:23:49.446 CORS policy execution successful. 
2025/08/25 09:23:49.446 Executing endpoint '/socket'. 
2025/08/25 09:23:50.875 ALC:1#PLC点位开始轮询
2025/08/25 09:23:50.876 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:23:50.877 ALC:后台服务关闭
2025/08/25 09:23:50.877 ALC:1#PLC点位轮询被关闭
2025/08/25 09:23:50.906 ALC:获取数据服务停止
2025/08/25 09:23:58.901 ALC:后台服务启动成功
2025/08/25 09:23:58.911 ALC:启动自动报警监控
2025/08/25 09:23:58.938 ALC:自动报警监控被关闭
2025/08/25 09:23:58.938 ALC:自动重启服务成功
2025/08/25 09:23:58.939 ALC:启动获取数据服务
2025/08/25 09:24:00.919 ALC:1#PLC点位开始轮询
2025/08/25 09:24:00.920 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:00.920 ALC:后台服务关闭
2025/08/25 09:24:00.921 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:00.965 ALC:获取数据服务停止
2025/08/25 09:24:08.953 ALC:后台服务启动成功
2025/08/25 09:24:08.964 ALC:自动重启服务成功
2025/08/25 09:24:08.964 ALC:启动自动报警监控
2025/08/25 09:24:08.965 ALC:自动报警监控被关闭
2025/08/25 09:24:08.965 ALC:启动获取数据服务
2025/08/25 09:24:10.971 ALC:1#PLC点位开始轮询
2025/08/25 09:24:10.972 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:10.972 ALC:后台服务关闭
2025/08/25 09:24:10.973 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:10.977 ALC:获取数据服务停止
2025/08/25 09:24:18.971 ALC:后台服务启动成功
2025/08/25 09:24:18.981 ALC:自动重启服务成功
2025/08/25 09:24:18.981 ALC:启动自动报警监控
2025/08/25 09:24:18.982 ALC:自动报警监控被关闭
2025/08/25 09:24:18.983 ALC:启动获取数据服务
2025/08/25 09:24:20.987 ALC:1#PLC点位开始轮询
2025/08/25 09:24:20.990 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:20.991 ALC:后台服务关闭
2025/08/25 09:24:20.991 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:21.999 ALC:获取数据服务停止
2025/08/25 09:24:23.936 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:24:28.997 ALC:后台服务启动成功
2025/08/25 09:24:29.008 ALC:自动重启服务成功
2025/08/25 09:24:29.009 ALC:启动自动报警监控
2025/08/25 09:24:29.009 ALC:自动报警监控被关闭
2025/08/25 09:24:29.010 ALC:启动获取数据服务
2025/08/25 09:24:31.016 ALC:1#PLC点位开始轮询
2025/08/25 09:24:31.017 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:31.018 ALC:后台服务关闭
2025/08/25 09:24:31.018 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:31.028 ALC:获取数据服务停止
2025/08/25 09:24:39.025 ALC:后台服务启动成功
2025/08/25 09:24:39.035 ALC:自动重启服务成功
2025/08/25 09:24:39.036 ALC:启动自动报警监控
2025/08/25 09:24:39.036 ALC:自动报警监控被关闭
2025/08/25 09:24:39.037 ALC:启动获取数据服务
2025/08/25 09:24:41.044 ALC:1#PLC点位开始轮询
2025/08/25 09:24:41.045 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:41.045 ALC:后台服务关闭
2025/08/25 09:24:41.046 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:41.050 ALC:获取数据服务停止
2025/08/25 09:24:49.048 ALC:后台服务启动成功
2025/08/25 09:24:49.057 ALC:自动重启服务成功
2025/08/25 09:24:49.058 ALC:启动自动报警监控
2025/08/25 09:24:49.058 ALC:自动报警监控被关闭
2025/08/25 09:24:49.058 ALC:启动获取数据服务
2025/08/25 09:24:51.065 ALC:1#PLC点位开始轮询
2025/08/25 09:24:51.066 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:24:51.066 ALC:后台服务关闭
2025/08/25 09:24:51.067 ALC:1#PLC点位轮询被关闭
2025/08/25 09:24:51.078 ALC:获取数据服务停止
2025/08/25 09:24:59.075 ALC:后台服务启动成功
2025/08/25 09:24:59.089 ALC:启动自动报警监控
2025/08/25 09:24:59.092 ALC:自动报警监控被关闭
2025/08/25 09:24:59.092 ALC:自动重启服务成功
2025/08/25 09:24:59.093 ALC:启动获取数据服务
2025/08/25 09:24:59.396 Executed endpoint '/socket'. 
2025/08/25 09:24:59.396 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=kSymop5Vdm4V-z8jFBJF3w - - - 101 - - 69951.8481ms. 
2025/08/25 09:25:00.575 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:25:00.576 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.5690ms. 
2025/08/25 09:25:00.716 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:25:00.716 CORS policy execution successful. 
2025/08/25 09:25:00.719 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:25:00.733 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:25:00.734 CORS policy execution successful. 
2025/08/25 09:25:00.734 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.5466ms. 
2025/08/25 09:25:00.737 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:25:00.737 CORS policy execution successful. 
2025/08/25 09:25:00.737 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:25:00.738 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:25:00.738 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:25:00.738 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.1444ms. 
2025/08/25 09:25:00.754 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:25:00.755 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 39.3028ms. 
2025/08/25 09:25:00.790 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=WHlpU3LriEjT5NPF9gEKkQ - -. 
2025/08/25 09:25:00.792 CORS policy execution successful. 
2025/08/25 09:25:00.793 Executing endpoint '/socket'. 
2025/08/25 09:25:01.105 ALC:1#PLC点位开始轮询
2025/08/25 09:25:01.107 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:01.108 ALC:后台服务关闭
2025/08/25 09:25:01.108 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:01.114 ALC:获取数据服务停止
2025/08/25 09:25:09.098 ALC:后台服务启动成功
2025/08/25 09:25:09.107 ALC:自动重启服务成功
2025/08/25 09:25:09.109 ALC:启动自动报警监控
2025/08/25 09:25:09.109 ALC:自动报警监控被关闭
2025/08/25 09:25:09.110 ALC:启动获取数据服务
2025/08/25 09:25:11.108 ALC:1#PLC点位开始轮询
2025/08/25 09:25:11.109 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:11.110 ALC:后台服务关闭
2025/08/25 09:25:11.111 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:11.123 ALC:获取数据服务停止
2025/08/25 09:25:19.113 ALC:后台服务启动成功
2025/08/25 09:25:19.121 ALC:自动重启服务成功
2025/08/25 09:25:19.122 ALC:启动自动报警监控
2025/08/25 09:25:19.122 ALC:自动报警监控被关闭
2025/08/25 09:25:19.123 ALC:启动获取数据服务
2025/08/25 09:25:21.127 ALC:1#PLC点位开始轮询
2025/08/25 09:25:21.130 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:21.131 ALC:后台服务关闭
2025/08/25 09:25:21.131 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:22.143 ALC:获取数据服务停止
2025/08/25 09:25:29.127 ALC:后台服务启动成功
2025/08/25 09:25:29.135 ALC:自动重启服务成功
2025/08/25 09:25:29.136 ALC:启动自动报警监控
2025/08/25 09:25:29.136 ALC:自动报警监控被关闭
2025/08/25 09:25:29.137 ALC:启动获取数据服务
2025/08/25 09:25:31.148 ALC:1#PLC点位开始轮询
2025/08/25 09:25:31.150 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:31.151 ALC:后台服务关闭
2025/08/25 09:25:31.151 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:32.155 ALC:获取数据服务停止
2025/08/25 09:25:39.141 ALC:后台服务启动成功
2025/08/25 09:25:39.150 ALC:自动重启服务成功
2025/08/25 09:25:39.151 ALC:启动自动报警监控
2025/08/25 09:25:39.151 ALC:自动报警监控被关闭
2025/08/25 09:25:39.152 ALC:启动获取数据服务
2025/08/25 09:25:41.147 ALC:1#PLC点位开始轮询
2025/08/25 09:25:41.148 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:41.148 ALC:后台服务关闭
2025/08/25 09:25:41.149 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:41.165 ALC:获取数据服务停止
2025/08/25 09:25:48.740 Executed endpoint '/socket'. 
2025/08/25 09:25:48.740 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=WHlpU3LriEjT5NPF9gEKkQ - - - 101 - - 47950.1915ms. 
2025/08/25 09:25:49.163 ALC:后台服务启动成功
2025/08/25 09:25:49.182 ALC:启动自动报警监控
2025/08/25 09:25:49.183 ALC:自动报警监控被关闭
2025/08/25 09:25:49.196 ALC:自动重启服务成功
2025/08/25 09:25:49.197 ALC:启动获取数据服务
2025/08/25 09:25:49.599 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:25:49.600 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.6623ms. 
2025/08/25 09:25:49.667 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:25:49.673 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:25:49.675 CORS policy execution successful. 
2025/08/25 09:25:49.675 CORS policy execution successful. 
2025/08/25 09:25:49.675 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 7.4520ms. 
2025/08/25 09:25:49.677 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:25:49.683 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:25:49.684 CORS policy execution successful. 
2025/08/25 09:25:49.684 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:25:49.684 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:25:49.685 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.8471ms. 
2025/08/25 09:25:49.698 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:25:49.716 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=G7LdiRpbo-cPCAqu4HwTCg - -. 
2025/08/25 09:25:49.716 CORS policy execution successful. 
2025/08/25 09:25:49.716 Executing endpoint '/socket'. 
2025/08/25 09:25:49.727 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:25:49.730 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 59.7212ms. 
2025/08/25 09:25:51.176 ALC:1#PLC点位开始轮询
2025/08/25 09:25:51.177 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:25:51.177 ALC:后台服务关闭
2025/08/25 09:25:51.177 ALC:1#PLC点位轮询被关闭
2025/08/25 09:25:51.207 ALC:获取数据服务停止
2025/08/25 09:25:55.996 Executed endpoint '/socket'. 
2025/08/25 09:25:56.001 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=G7LdiRpbo-cPCAqu4HwTCg - - - 101 - - 6285.0786ms. 
2025/08/25 09:25:56.744 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:25:56.745 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.7594ms. 
2025/08/25 09:25:56.796 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:25:56.797 CORS policy execution successful. 
2025/08/25 09:25:56.814 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:25:56.815 CORS policy execution successful. 
2025/08/25 09:25:56.815 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 2.6612ms. 
2025/08/25 09:25:56.817 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:25:56.818 CORS policy execution successful. 
2025/08/25 09:25:56.819 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:25:56.824 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:25:56.832 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:25:56.834 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 17.1036ms. 
2025/08/25 09:25:56.844 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=dJH6MIPrQ19j2dJpNiHUSA - -. 
2025/08/25 09:25:56.845 CORS policy execution successful. 
2025/08/25 09:25:56.845 Executing endpoint '/socket'. 
2025/08/25 09:25:56.866 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:25:56.890 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:25:56.891 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 94.8381ms. 
2025/08/25 09:25:59.213 ALC:后台服务启动成功
2025/08/25 09:25:59.222 ALC:启动自动报警监控
2025/08/25 09:25:59.224 ALC:自动报警监控被关闭
2025/08/25 09:25:59.225 ALC:自动重启服务成功
2025/08/25 09:25:59.225 ALC:启动获取数据服务
2025/08/25 09:26:01.228 ALC:1#PLC点位开始轮询
2025/08/25 09:26:01.231 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:01.231 ALC:后台服务关闭
2025/08/25 09:26:01.231 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:02.232 ALC:获取数据服务停止
2025/08/25 09:26:03.344 Executed endpoint '/socket'. 
2025/08/25 09:26:03.345 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=dJH6MIPrQ19j2dJpNiHUSA - - - 101 - - 6500.3004ms. 
2025/08/25 09:26:03.991 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:26:03.993 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 2.1295ms. 
2025/08/25 09:26:04.075 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:26:04.075 CORS policy execution successful. 
2025/08/25 09:26:04.077 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:26:04.087 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:26:04.088 CORS policy execution successful. 
2025/08/25 09:26:04.089 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 2.3371ms. 
2025/08/25 09:26:04.092 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:26:04.092 CORS policy execution successful. 
2025/08/25 09:26:04.093 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:26:04.093 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:26:04.093 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.2991ms. 
2025/08/25 09:26:04.107 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:26:04.116 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=Bi-uSo-Gkhd6hiPP_NQ9Iw - -. 
2025/08/25 09:26:04.116 CORS policy execution successful. 
2025/08/25 09:26:04.116 Executing endpoint '/socket'. 
2025/08/25 09:26:04.128 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:26:04.129 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 54.1231ms. 
2025/08/25 09:26:09.323 ALC:后台服务启动成功
2025/08/25 09:26:09.331 ALC:启动自动报警监控
2025/08/25 09:26:09.333 ALC:自动报警监控被关闭
2025/08/25 09:26:09.334 ALC:自动重启服务成功
2025/08/25 09:26:09.335 ALC:启动获取数据服务
2025/08/25 09:26:11.340 ALC:1#PLC点位开始轮询
2025/08/25 09:26:11.343 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:11.344 ALC:后台服务关闭
2025/08/25 09:26:11.345 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:12.355 ALC:获取数据服务停止
2025/08/25 09:26:19.344 ALC:后台服务启动成功
2025/08/25 09:26:19.353 ALC:启动自动报警监控
2025/08/25 09:26:19.378 ALC:自动报警监控被关闭
2025/08/25 09:26:19.379 ALC:自动重启服务成功
2025/08/25 09:26:19.379 ALC:启动获取数据服务
2025/08/25 09:26:21.353 ALC:1#PLC点位开始轮询
2025/08/25 09:26:21.353 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:21.354 ALC:后台服务关闭
2025/08/25 09:26:21.354 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:21.383 ALC:获取数据服务停止
2025/08/25 09:26:29.390 ALC:后台服务启动成功
2025/08/25 09:26:29.400 ALC:自动重启服务成功
2025/08/25 09:26:29.400 ALC:启动自动报警监控
2025/08/25 09:26:29.401 ALC:自动报警监控被关闭
2025/08/25 09:26:29.401 ALC:启动获取数据服务
2025/08/25 09:26:31.412 ALC:1#PLC点位开始轮询
2025/08/25 09:26:31.413 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:31.414 ALC:后台服务关闭
2025/08/25 09:26:31.415 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:32.418 ALC:获取数据服务停止
2025/08/25 09:26:39.414 ALC:后台服务启动成功
2025/08/25 09:26:39.423 ALC:自动重启服务成功
2025/08/25 09:26:39.427 ALC:启动自动报警监控
2025/08/25 09:26:39.428 ALC:自动报警监控被关闭
2025/08/25 09:26:39.428 ALC:启动获取数据服务
2025/08/25 09:26:41.421 ALC:1#PLC点位开始轮询
2025/08/25 09:26:41.422 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:41.423 ALC:后台服务关闭
2025/08/25 09:26:41.423 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:41.440 ALC:获取数据服务停止
2025/08/25 09:26:49.431 ALC:后台服务启动成功
2025/08/25 09:26:49.444 ALC:自动重启服务成功
2025/08/25 09:26:49.445 ALC:启动自动报警监控
2025/08/25 09:26:49.446 ALC:自动报警监控被关闭
2025/08/25 09:26:49.447 ALC:启动获取数据服务
2025/08/25 09:26:51.442 ALC:1#PLC点位开始轮询
2025/08/25 09:26:51.443 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:26:51.444 ALC:后台服务关闭
2025/08/25 09:26:51.445 ALC:1#PLC点位轮询被关闭
2025/08/25 09:26:51.458 ALC:获取数据服务停止
2025/08/25 09:26:59.457 ALC:后台服务启动成功
2025/08/25 09:26:59.467 ALC:启动自动报警监控
2025/08/25 09:26:59.470 ALC:自动报警监控被关闭
2025/08/25 09:26:59.470 ALC:自动重启服务成功
2025/08/25 09:26:59.471 ALC:启动获取数据服务
2025/08/25 09:27:01.481 ALC:1#PLC点位开始轮询
2025/08/25 09:27:01.482 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:01.483 ALC:后台服务关闭
2025/08/25 09:27:01.483 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:02.480 ALC:获取数据服务停止
2025/08/25 09:27:09.478 ALC:后台服务启动成功
2025/08/25 09:27:09.488 ALC:自动重启服务成功
2025/08/25 09:27:09.488 ALC:启动自动报警监控
2025/08/25 09:27:09.489 ALC:自动报警监控被关闭
2025/08/25 09:27:09.489 ALC:启动获取数据服务
2025/08/25 09:27:11.493 ALC:1#PLC点位开始轮询
2025/08/25 09:27:11.496 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:11.496 ALC:后台服务关闭
2025/08/25 09:27:11.497 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:12.505 ALC:获取数据服务停止
2025/08/25 09:27:19.500 ALC:后台服务启动成功
2025/08/25 09:27:19.512 ALC:启动自动报警监控
2025/08/25 09:27:19.516 ALC:自动报警监控被关闭
2025/08/25 09:27:19.516 ALC:启动获取数据服务
2025/08/25 09:27:19.517 ALC:自动重启服务成功
2025/08/25 09:27:21.515 ALC:1#PLC点位开始轮询
2025/08/25 09:27:21.516 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:21.517 ALC:后台服务关闭
2025/08/25 09:27:21.518 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:21.531 ALC:获取数据服务停止
2025/08/25 09:27:25.100 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:27:30.044 ALC:后台服务启动成功
2025/08/25 09:27:30.056 ALC:自动重启服务成功
2025/08/25 09:27:30.056 ALC:启动自动报警监控
2025/08/25 09:27:30.057 ALC:自动报警监控被关闭
2025/08/25 09:27:30.061 ALC:启动获取数据服务
2025/08/25 09:27:32.055 ALC:1#PLC点位开始轮询
2025/08/25 09:27:32.056 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:32.057 ALC:后台服务关闭
2025/08/25 09:27:32.058 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:32.086 ALC:获取数据服务停止
2025/08/25 09:27:38.399 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:27:40.061 ALC:后台服务启动成功
2025/08/25 09:27:40.070 ALC:启动自动报警监控
2025/08/25 09:27:40.104 ALC:自动报警监控被关闭
2025/08/25 09:27:40.104 ALC:自动重启服务成功
2025/08/25 09:27:40.105 ALC:启动获取数据服务
2025/08/25 09:27:42.071 ALC:1#PLC点位开始轮询
2025/08/25 09:27:42.073 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:42.074 ALC:后台服务关闭
2025/08/25 09:27:42.074 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:42.119 ALC:获取数据服务停止
2025/08/25 09:27:50.117 ALC:后台服务启动成功
2025/08/25 09:27:50.128 ALC:自动重启服务成功
2025/08/25 09:27:50.128 ALC:启动自动报警监控
2025/08/25 09:27:50.129 ALC:自动报警监控被关闭
2025/08/25 09:27:50.129 ALC:启动获取数据服务
2025/08/25 09:27:54.494 ALC:1#PLC点位开始轮询
2025/08/25 09:27:54.512 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:27:54.521 ALC:后台服务关闭
2025/08/25 09:27:54.522 ALC:1#PLC点位轮询被关闭
2025/08/25 09:27:54.526 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:27:55.509 ALC:获取数据服务停止
2025/08/25 09:28:00.139 ALC:后台服务启动成功
2025/08/25 09:28:00.149 ALC:启动自动报警监控
2025/08/25 09:28:00.180 ALC:自动报警监控被关闭
2025/08/25 09:28:00.181 ALC:自动重启服务成功
2025/08/25 09:28:00.181 ALC:启动获取数据服务
2025/08/25 09:28:02.154 ALC:1#PLC点位开始轮询
2025/08/25 09:28:02.154 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:02.155 ALC:后台服务关闭
2025/08/25 09:28:02.155 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:02.201 ALC:获取数据服务停止
2025/08/25 09:28:10.196 ALC:后台服务启动成功
2025/08/25 09:28:10.206 ALC:自动重启服务成功
2025/08/25 09:28:10.206 ALC:启动自动报警监控
2025/08/25 09:28:10.207 ALC:自动报警监控被关闭
2025/08/25 09:28:10.207 ALC:启动获取数据服务
2025/08/25 09:28:12.218 ALC:1#PLC点位开始轮询
2025/08/25 09:28:12.219 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:12.220 ALC:后台服务关闭
2025/08/25 09:28:12.220 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:13.221 ALC:获取数据服务停止
2025/08/25 09:28:20.218 ALC:后台服务启动成功
2025/08/25 09:28:20.228 ALC:自动重启服务成功
2025/08/25 09:28:20.229 ALC:启动自动报警监控
2025/08/25 09:28:20.229 ALC:自动报警监控被关闭
2025/08/25 09:28:20.229 ALC:启动获取数据服务
2025/08/25 09:28:22.229 ALC:1#PLC点位开始轮询
2025/08/25 09:28:22.229 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:22.230 ALC:后台服务关闭
2025/08/25 09:28:22.230 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:22.245 ALC:获取数据服务停止
2025/08/25 09:28:30.242 ALC:后台服务启动成功
2025/08/25 09:28:30.250 ALC:启动自动报警监控
2025/08/25 09:28:30.288 ALC:自动报警监控被关闭
2025/08/25 09:28:30.288 ALC:自动重启服务成功
2025/08/25 09:28:30.289 ALC:启动获取数据服务
2025/08/25 09:28:32.258 ALC:1#PLC点位开始轮询
2025/08/25 09:28:32.258 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:32.259 ALC:后台服务关闭
2025/08/25 09:28:32.259 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:32.307 ALC:获取数据服务停止
2025/08/25 09:28:40.305 ALC:后台服务启动成功
2025/08/25 09:28:40.316 ALC:自动重启服务成功
2025/08/25 09:28:40.317 ALC:启动自动报警监控
2025/08/25 09:28:40.317 ALC:自动报警监控被关闭
2025/08/25 09:28:40.318 ALC:启动获取数据服务
2025/08/25 09:28:42.325 ALC:1#PLC点位开始轮询
2025/08/25 09:28:42.325 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:42.326 ALC:后台服务关闭
2025/08/25 09:28:42.326 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:43.324 ALC:获取数据服务停止
2025/08/25 09:28:50.326 ALC:后台服务启动成功
2025/08/25 09:28:50.337 ALC:自动重启服务成功
2025/08/25 09:28:50.337 ALC:启动自动报警监控
2025/08/25 09:28:50.338 ALC:自动报警监控被关闭
2025/08/25 09:28:50.338 ALC:启动获取数据服务
2025/08/25 09:28:52.335 ALC:1#PLC点位开始轮询
2025/08/25 09:28:52.336 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:28:52.337 ALC:后台服务关闭
2025/08/25 09:28:52.337 ALC:1#PLC点位轮询被关闭
2025/08/25 09:28:52.367 ALC:获取数据服务停止
2025/08/25 09:29:00.343 ALC:后台服务启动成功
2025/08/25 09:29:00.354 ALC:启动自动报警监控
2025/08/25 09:29:00.355 ALC:自动报警监控被关闭
2025/08/25 09:29:00.356 ALC:启动获取数据服务
2025/08/25 09:29:00.356 ALC:自动重启服务成功
2025/08/25 09:29:02.365 ALC:1#PLC点位开始轮询
2025/08/25 09:29:02.365 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:02.366 ALC:后台服务关闭
2025/08/25 09:29:02.366 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:02.599 Executed endpoint '/socket'. 
2025/08/25 09:29:02.612 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=Bi-uSo-Gkhd6hiPP_NQ9Iw - - - 101 - - 178496.0763ms. 
2025/08/25 09:29:03.373 ALC:获取数据服务停止
2025/08/25 09:29:03.490 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:29:03.491 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.8046ms. 
2025/08/25 09:29:03.597 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:29:03.598 CORS policy execution successful. 
2025/08/25 09:29:03.600 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:29:03.605 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:29:03.605 CORS policy execution successful. 
2025/08/25 09:29:03.607 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.9859ms. 
2025/08/25 09:29:03.614 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:29:03.615 CORS policy execution successful. 
2025/08/25 09:29:03.615 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:29:03.616 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:29:03.617 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.1477ms. 
2025/08/25 09:29:03.627 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=nAikr_axRZbaN8fz1J0nfQ - -. 
2025/08/25 09:29:03.627 CORS policy execution successful. 
2025/08/25 09:29:03.628 Executing endpoint '/socket'. 
2025/08/25 09:29:03.632 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:29:03.648 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:29:03.648 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 50.6548ms. 
2025/08/25 09:29:10.371 ALC:后台服务启动成功
2025/08/25 09:29:10.379 ALC:启动自动报警监控
2025/08/25 09:29:10.381 ALC:自动报警监控被关闭
2025/08/25 09:29:10.382 ALC:自动重启服务成功
2025/08/25 09:29:10.382 ALC:启动获取数据服务
2025/08/25 09:29:12.386 ALC:1#PLC点位开始轮询
2025/08/25 09:29:12.389 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:12.389 ALC:后台服务关闭
2025/08/25 09:29:12.389 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:12.417 ALC:获取数据服务停止
2025/08/25 09:29:20.397 ALC:后台服务启动成功
2025/08/25 09:29:20.406 ALC:自动重启服务成功
2025/08/25 09:29:20.408 ALC:启动自动报警监控
2025/08/25 09:29:20.409 ALC:自动报警监控被关闭
2025/08/25 09:29:20.409 ALC:启动获取数据服务
2025/08/25 09:29:22.418 ALC:1#PLC点位开始轮询
2025/08/25 09:29:22.419 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:22.420 ALC:后台服务关闭
2025/08/25 09:29:22.420 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:22.423 ALC:获取数据服务停止
2025/08/25 09:29:30.417 ALC:后台服务启动成功
2025/08/25 09:29:30.427 ALC:自动重启服务成功
2025/08/25 09:29:30.428 ALC:启动自动报警监控
2025/08/25 09:29:30.428 ALC:自动报警监控被关闭
2025/08/25 09:29:30.428 ALC:启动获取数据服务
2025/08/25 09:29:32.434 ALC:1#PLC点位开始轮询
2025/08/25 09:29:32.435 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:32.435 ALC:后台服务关闭
2025/08/25 09:29:32.436 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:32.440 ALC:获取数据服务停止
2025/08/25 09:29:40.437 ALC:后台服务启动成功
2025/08/25 09:29:40.450 ALC:自动重启服务成功
2025/08/25 09:29:40.450 ALC:启动自动报警监控
2025/08/25 09:29:40.450 ALC:自动报警监控被关闭
2025/08/25 09:29:40.451 ALC:启动获取数据服务
2025/08/25 09:29:42.444 ALC:1#PLC点位开始轮询
2025/08/25 09:29:42.444 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:42.445 ALC:后台服务关闭
2025/08/25 09:29:42.445 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:42.461 ALC:获取数据服务停止
2025/08/25 09:29:50.464 ALC:后台服务启动成功
2025/08/25 09:29:50.477 ALC:自动重启服务成功
2025/08/25 09:29:50.477 ALC:启动自动报警监控
2025/08/25 09:29:50.478 ALC:自动报警监控被关闭
2025/08/25 09:29:50.478 ALC:启动获取数据服务
2025/08/25 09:29:52.485 ALC:1#PLC点位开始轮询
2025/08/25 09:29:52.486 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:29:52.486 ALC:后台服务关闭
2025/08/25 09:29:52.487 ALC:1#PLC点位轮询被关闭
2025/08/25 09:29:53.491 ALC:获取数据服务停止
2025/08/25 09:30:00.481 ALC:后台服务启动成功
2025/08/25 09:30:00.491 ALC:启动自动报警监控
2025/08/25 09:30:00.493 ALC:自动报警监控被关闭
2025/08/25 09:30:00.494 ALC:自动重启服务成功
2025/08/25 09:30:00.494 ALC:启动获取数据服务
2025/08/25 09:30:02.501 ALC:1#PLC点位开始轮询
2025/08/25 09:30:02.502 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:02.502 ALC:后台服务关闭
2025/08/25 09:30:02.503 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:02.513 ALC:获取数据服务停止
2025/08/25 09:30:10.499 ALC:后台服务启动成功
2025/08/25 09:30:10.512 ALC:自动重启服务成功
2025/08/25 09:30:10.512 ALC:启动自动报警监控
2025/08/25 09:30:10.513 ALC:启动获取数据服务
2025/08/25 09:30:10.514 ALC:自动报警监控被关闭
2025/08/25 09:30:12.508 ALC:1#PLC点位开始轮询
2025/08/25 09:30:12.509 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:12.509 ALC:后台服务关闭
2025/08/25 09:30:12.509 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:12.527 ALC:获取数据服务停止
2025/08/25 09:30:20.519 ALC:后台服务启动成功
2025/08/25 09:30:20.529 ALC:启动自动报警监控
2025/08/25 09:30:20.531 ALC:自动报警监控被关闭
2025/08/25 09:30:20.532 ALC:自动重启服务成功
2025/08/25 09:30:20.532 ALC:启动获取数据服务
2025/08/25 09:30:22.535 ALC:1#PLC点位开始轮询
2025/08/25 09:30:22.536 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:22.536 ALC:后台服务关闭
2025/08/25 09:30:22.537 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:22.548 ALC:获取数据服务停止
2025/08/25 09:30:30.537 ALC:后台服务启动成功
2025/08/25 09:30:30.551 ALC:自动重启服务成功
2025/08/25 09:30:30.551 ALC:启动自动报警监控
2025/08/25 09:30:30.552 ALC:自动报警监控被关闭
2025/08/25 09:30:30.552 ALC:启动获取数据服务
2025/08/25 09:30:32.559 ALC:1#PLC点位开始轮询
2025/08/25 09:30:32.559 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:32.560 ALC:后台服务关闭
2025/08/25 09:30:32.560 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:32.564 ALC:获取数据服务停止
2025/08/25 09:30:40.562 ALC:后台服务启动成功
2025/08/25 09:30:40.573 ALC:自动重启服务成功
2025/08/25 09:30:40.574 ALC:启动自动报警监控
2025/08/25 09:30:40.574 ALC:自动报警监控被关闭
2025/08/25 09:30:40.575 ALC:启动获取数据服务
2025/08/25 09:30:42.571 ALC:1#PLC点位开始轮询
2025/08/25 09:30:42.572 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:42.573 ALC:后台服务关闭
2025/08/25 09:30:42.573 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:42.601 ALC:获取数据服务停止
2025/08/25 09:30:50.589 ALC:后台服务启动成功
2025/08/25 09:30:50.599 ALC:自动重启服务成功
2025/08/25 09:30:50.600 ALC:启动自动报警监控
2025/08/25 09:30:50.600 ALC:自动报警监控被关闭
2025/08/25 09:30:50.600 ALC:启动获取数据服务
2025/08/25 09:30:52.598 ALC:1#PLC点位开始轮询
2025/08/25 09:30:52.599 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:30:52.599 ALC:后台服务关闭
2025/08/25 09:30:52.600 ALC:1#PLC点位轮询被关闭
2025/08/25 09:30:52.613 ALC:获取数据服务停止
2025/08/25 09:31:00.607 ALC:后台服务启动成功
2025/08/25 09:31:00.619 ALC:自动重启服务成功
2025/08/25 09:31:00.620 ALC:启动自动报警监控
2025/08/25 09:31:00.620 ALC:自动报警监控被关闭
2025/08/25 09:31:00.621 ALC:启动获取数据服务
2025/08/25 09:31:02.627 ALC:1#PLC点位开始轮询
2025/08/25 09:31:02.628 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:02.628 ALC:后台服务关闭
2025/08/25 09:31:02.629 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:02.630 ALC:获取数据服务停止
2025/08/25 09:31:10.636 ALC:后台服务启动成功
2025/08/25 09:31:10.646 ALC:自动重启服务成功
2025/08/25 09:31:10.647 ALC:启动获取数据服务
2025/08/25 09:31:10.647 ALC:启动自动报警监控
2025/08/25 09:31:10.648 ALC:自动报警监控被关闭
2025/08/25 09:31:12.652 ALC:1#PLC点位开始轮询
2025/08/25 09:31:12.653 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:12.653 ALC:后台服务关闭
2025/08/25 09:31:12.653 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:13.656 ALC:获取数据服务停止
2025/08/25 09:31:20.655 ALC:后台服务启动成功
2025/08/25 09:31:20.665 ALC:启动自动报警监控
2025/08/25 09:31:20.668 ALC:自动报警监控被关闭
2025/08/25 09:31:20.668 ALC:自动重启服务成功
2025/08/25 09:31:20.669 ALC:启动获取数据服务
2025/08/25 09:31:22.675 ALC:1#PLC点位开始轮询
2025/08/25 09:31:22.675 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:22.676 ALC:后台服务关闭
2025/08/25 09:31:22.676 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:22.677 ALC:获取数据服务停止
2025/08/25 09:31:30.678 ALC:后台服务启动成功
2025/08/25 09:31:30.689 ALC:自动重启服务成功
2025/08/25 09:31:30.690 ALC:启动自动报警监控
2025/08/25 09:31:30.691 ALC:自动报警监控被关闭
2025/08/25 09:31:30.692 ALC:启动获取数据服务
2025/08/25 09:31:32.700 ALC:1#PLC点位开始轮询
2025/08/25 09:31:32.701 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:32.701 ALC:后台服务关闭
2025/08/25 09:31:32.702 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:33.700 ALC:获取数据服务停止
2025/08/25 09:31:37.326 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:31:42.261 ALC:后台服务启动成功
2025/08/25 09:31:42.278 ALC:自动重启服务成功
2025/08/25 09:31:42.279 ALC:启动自动报警监控
2025/08/25 09:31:42.279 ALC:自动报警监控被关闭
2025/08/25 09:31:42.280 ALC:启动获取数据服务
2025/08/25 09:31:44.286 ALC:1#PLC点位开始轮询
2025/08/25 09:31:44.287 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:44.287 ALC:后台服务关闭
2025/08/25 09:31:44.288 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:45.299 ALC:获取数据服务停止
2025/08/25 09:31:52.286 ALC:后台服务启动成功
2025/08/25 09:31:52.298 ALC:自动重启服务成功
2025/08/25 09:31:52.298 ALC:启动自动报警监控
2025/08/25 09:31:52.299 ALC:自动报警监控被关闭
2025/08/25 09:31:52.299 ALC:启动获取数据服务
2025/08/25 09:31:54.308 ALC:1#PLC点位开始轮询
2025/08/25 09:31:54.309 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:31:54.310 ALC:后台服务关闭
2025/08/25 09:31:54.310 ALC:1#PLC点位轮询被关闭
2025/08/25 09:31:55.322 ALC:获取数据服务停止
2025/08/25 09:32:02.305 ALC:后台服务启动成功
2025/08/25 09:32:02.315 ALC:启动自动报警监控
2025/08/25 09:32:02.345 ALC:自动报警监控被关闭
2025/08/25 09:32:02.345 ALC:自动重启服务成功
2025/08/25 09:32:02.346 ALC:启动获取数据服务
2025/08/25 09:32:04.317 ALC:1#PLC点位开始轮询
2025/08/25 09:32:04.318 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:04.318 ALC:后台服务关闭
2025/08/25 09:32:04.319 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:04.353 ALC:获取数据服务停止
2025/08/25 09:32:12.352 ALC:后台服务启动成功
2025/08/25 09:32:12.362 ALC:自动重启服务成功
2025/08/25 09:32:12.362 ALC:启动自动报警监控
2025/08/25 09:32:12.363 ALC:自动报警监控被关闭
2025/08/25 09:32:12.363 ALC:启动获取数据服务
2025/08/25 09:32:14.373 ALC:1#PLC点位开始轮询
2025/08/25 09:32:14.374 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:14.374 ALC:后台服务关闭
2025/08/25 09:32:14.375 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:14.379 ALC:获取数据服务停止
2025/08/25 09:32:22.376 ALC:后台服务启动成功
2025/08/25 09:32:22.393 ALC:自动重启服务成功
2025/08/25 09:32:22.394 ALC:启动自动报警监控
2025/08/25 09:32:22.395 ALC:自动报警监控被关闭
2025/08/25 09:32:22.396 ALC:启动获取数据服务
2025/08/25 09:32:24.392 ALC:1#PLC点位开始轮询
2025/08/25 09:32:24.393 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:24.394 ALC:后台服务关闭
2025/08/25 09:32:24.394 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:24.408 ALC:获取数据服务停止
2025/08/25 09:32:32.533 ALC:后台服务启动成功
2025/08/25 09:32:32.551 ALC:自动重启服务成功
2025/08/25 09:32:32.553 ALC:启动自动报警监控
2025/08/25 09:32:32.555 ALC:启动获取数据服务
2025/08/25 09:32:32.556 ALC:自动报警监控被关闭
2025/08/25 09:32:34.542 ALC:1#PLC点位开始轮询
2025/08/25 09:32:34.542 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:34.543 ALC:后台服务关闭
2025/08/25 09:32:34.544 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:34.574 ALC:获取数据服务停止
2025/08/25 09:32:42.632 ALC:后台服务启动成功
2025/08/25 09:32:42.650 ALC:自动重启服务成功
2025/08/25 09:32:42.651 ALC:启动自动报警监控
2025/08/25 09:32:42.652 ALC:自动报警监控被关闭
2025/08/25 09:32:42.653 ALC:启动获取数据服务
2025/08/25 09:32:44.651 ALC:1#PLC点位开始轮询
2025/08/25 09:32:44.658 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:44.669 ALC:后台服务关闭
2025/08/25 09:32:44.670 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:44.674 ALC:获取数据服务停止
2025/08/25 09:32:52.705 ALC:后台服务启动成功
2025/08/25 09:32:52.970 ALC:启动自动报警监控
2025/08/25 09:32:53.098 ALC:自动报警监控被关闭
2025/08/25 09:32:53.131 ALC:自动重启服务成功
2025/08/25 09:32:53.145 ALC:启动获取数据服务
2025/08/25 09:32:54.821 ALC:1#PLC点位开始轮询
2025/08/25 09:32:54.988 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:32:55.155 ALC:后台服务关闭
2025/08/25 09:32:55.214 ALC:1#PLC点位轮询被关闭
2025/08/25 09:32:55.256 ALC:获取数据服务停止
2025/08/25 09:33:03.166 ALC:后台服务启动成功
2025/08/25 09:33:03.199 ALC:自动重启服务成功
2025/08/25 09:33:03.200 ALC:启动自动报警监控
2025/08/25 09:33:03.200 ALC:自动报警监控被关闭
2025/08/25 09:33:03.201 ALC:启动获取数据服务
2025/08/25 09:33:05.187 ALC:1#PLC点位开始轮询
2025/08/25 09:33:05.188 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:05.188 ALC:后台服务关闭
2025/08/25 09:33:05.189 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:05.234 ALC:获取数据服务停止
2025/08/25 09:33:13.214 ALC:后台服务启动成功
2025/08/25 09:33:13.237 ALC:启动自动报警监控
2025/08/25 09:33:13.248 ALC:自动报警监控被关闭
2025/08/25 09:33:13.313 ALC:自动重启服务成功
2025/08/25 09:33:13.321 ALC:启动获取数据服务
2025/08/25 09:33:15.290 ALC:1#PLC点位开始轮询
2025/08/25 09:33:15.291 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:15.292 ALC:后台服务关闭
2025/08/25 09:33:15.293 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:15.350 ALC:获取数据服务停止
2025/08/25 09:33:23.348 ALC:后台服务启动成功
2025/08/25 09:33:23.464 ALC:启动自动报警监控
2025/08/25 09:33:23.496 ALC:自动报警监控被关闭
2025/08/25 09:33:23.542 ALC:自动重启服务成功
2025/08/25 09:33:23.549 ALC:启动获取数据服务
2025/08/25 09:33:25.445 ALC:1#PLC点位开始轮询
2025/08/25 09:33:25.446 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:25.446 ALC:后台服务关闭
2025/08/25 09:33:25.447 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:25.566 ALC:获取数据服务停止
2025/08/25 09:33:33.613 ALC:后台服务启动成功
2025/08/25 09:33:33.631 ALC:启动自动报警监控
2025/08/25 09:33:33.633 ALC:自动报警监控被关闭
2025/08/25 09:33:33.640 ALC:自动重启服务成功
2025/08/25 09:33:33.642 ALC:启动获取数据服务
2025/08/25 09:33:35.628 ALC:1#PLC点位开始轮询
2025/08/25 09:33:35.629 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:35.629 ALC:后台服务关闭
2025/08/25 09:33:35.630 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:35.657 ALC:获取数据服务停止
2025/08/25 09:33:43.665 ALC:后台服务启动成功
2025/08/25 09:33:43.681 ALC:自动重启服务成功
2025/08/25 09:33:43.682 ALC:启动自动报警监控
2025/08/25 09:33:43.683 ALC:自动报警监控被关闭
2025/08/25 09:33:43.684 ALC:启动获取数据服务
2025/08/25 09:33:45.683 ALC:1#PLC点位开始轮询
2025/08/25 09:33:45.684 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:45.685 ALC:后台服务关闭
2025/08/25 09:33:45.686 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:45.710 ALC:获取数据服务停止
2025/08/25 09:33:53.703 ALC:后台服务启动成功
2025/08/25 09:33:53.720 ALC:自动重启服务成功
2025/08/25 09:33:53.721 ALC:启动自动报警监控
2025/08/25 09:33:53.721 ALC:自动报警监控被关闭
2025/08/25 09:33:53.722 ALC:启动获取数据服务
2025/08/25 09:33:55.711 ALC:1#PLC点位开始轮询
2025/08/25 09:33:55.712 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:33:55.712 ALC:后台服务关闭
2025/08/25 09:33:55.713 ALC:1#PLC点位轮询被关闭
2025/08/25 09:33:55.730 ALC:获取数据服务停止
2025/08/25 09:34:03.725 ALC:后台服务启动成功
2025/08/25 09:34:03.735 ALC:启动自动报警监控
2025/08/25 09:34:03.738 ALC:自动报警监控被关闭
2025/08/25 09:34:03.738 ALC:自动重启服务成功
2025/08/25 09:34:03.739 ALC:启动获取数据服务
2025/08/25 09:34:05.742 ALC:1#PLC点位开始轮询
2025/08/25 09:34:05.743 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:05.744 ALC:后台服务关闭
2025/08/25 09:34:05.745 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:05.749 ALC:获取数据服务停止
2025/08/25 09:34:13.754 ALC:后台服务启动成功
2025/08/25 09:34:13.765 ALC:自动重启服务成功
2025/08/25 09:34:13.766 ALC:启动自动报警监控
2025/08/25 09:34:13.766 ALC:自动报警监控被关闭
2025/08/25 09:34:13.767 ALC:启动获取数据服务
2025/08/25 09:34:15.773 ALC:1#PLC点位开始轮询
2025/08/25 09:34:15.773 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:15.774 ALC:后台服务关闭
2025/08/25 09:34:15.774 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:15.787 ALC:获取数据服务停止
2025/08/25 09:34:23.782 ALC:后台服务启动成功
2025/08/25 09:34:23.793 ALC:启动自动报警监控
2025/08/25 09:34:23.825 ALC:自动报警监控被关闭
2025/08/25 09:34:23.826 ALC:自动重启服务成功
2025/08/25 09:34:23.826 ALC:启动获取数据服务
2025/08/25 09:34:25.800 ALC:1#PLC点位开始轮询
2025/08/25 09:34:25.803 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:25.805 ALC:后台服务关闭
2025/08/25 09:34:25.806 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:25.836 ALC:获取数据服务停止
2025/08/25 09:34:33.832 ALC:后台服务启动成功
2025/08/25 09:34:33.844 ALC:启动自动报警监控
2025/08/25 09:34:33.875 ALC:自动报警监控被关闭
2025/08/25 09:34:33.875 ALC:自动重启服务成功
2025/08/25 09:34:33.876 ALC:启动获取数据服务
2025/08/25 09:34:35.844 ALC:1#PLC点位开始轮询
2025/08/25 09:34:35.845 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:35.846 ALC:后台服务关闭
2025/08/25 09:34:35.846 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:35.893 ALC:获取数据服务停止
2025/08/25 09:34:43.884 ALC:后台服务启动成功
2025/08/25 09:34:43.898 ALC:自动重启服务成功
2025/08/25 09:34:43.898 ALC:启动自动报警监控
2025/08/25 09:34:43.899 ALC:自动报警监控被关闭
2025/08/25 09:34:43.899 ALC:启动获取数据服务
2025/08/25 09:34:45.904 ALC:1#PLC点位开始轮询
2025/08/25 09:34:45.905 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:45.905 ALC:后台服务关闭
2025/08/25 09:34:45.906 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:45.910 ALC:获取数据服务停止
2025/08/25 09:34:53.915 ALC:后台服务启动成功
2025/08/25 09:34:53.923 ALC:启动自动报警监控
2025/08/25 09:34:53.953 ALC:自动报警监控被关闭
2025/08/25 09:34:53.953 ALC:自动重启服务成功
2025/08/25 09:34:53.954 ALC:启动获取数据服务
2025/08/25 09:34:55.930 ALC:1#PLC点位开始轮询
2025/08/25 09:34:55.931 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:34:55.932 ALC:后台服务关闭
2025/08/25 09:34:55.932 ALC:1#PLC点位轮询被关闭
2025/08/25 09:34:55.974 ALC:获取数据服务停止
2025/08/25 09:35:04.004 ALC:后台服务启动成功
2025/08/25 09:35:04.019 ALC:自动重启服务成功
2025/08/25 09:35:04.020 ALC:启动自动报警监控
2025/08/25 09:35:04.020 ALC:自动报警监控被关闭
2025/08/25 09:35:04.020 ALC:启动获取数据服务
2025/08/25 09:35:06.020 ALC:1#PLC点位开始轮询
2025/08/25 09:35:06.022 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:06.023 ALC:后台服务关闭
2025/08/25 09:35:06.024 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:06.034 ALC:获取数据服务停止
2025/08/25 09:35:14.027 ALC:后台服务启动成功
2025/08/25 09:35:14.054 ALC:自动重启服务成功
2025/08/25 09:35:14.055 ALC:启动自动报警监控
2025/08/25 09:35:14.056 ALC:自动报警监控被关闭
2025/08/25 09:35:14.057 ALC:启动获取数据服务
2025/08/25 09:35:16.052 ALC:1#PLC点位开始轮询
2025/08/25 09:35:16.053 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:16.054 ALC:后台服务关闭
2025/08/25 09:35:16.055 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:16.082 ALC:获取数据服务停止
2025/08/25 09:35:24.069 ALC:后台服务启动成功
2025/08/25 09:35:24.079 ALC:自动重启服务成功
2025/08/25 09:35:24.079 ALC:启动自动报警监控
2025/08/25 09:35:24.080 ALC:自动报警监控被关闭
2025/08/25 09:35:24.080 ALC:启动获取数据服务
2025/08/25 09:35:26.092 ALC:1#PLC点位开始轮询
2025/08/25 09:35:26.093 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:26.093 ALC:后台服务关闭
2025/08/25 09:35:26.094 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:26.097 ALC:获取数据服务停止
2025/08/25 09:35:34.098 ALC:后台服务启动成功
2025/08/25 09:35:34.108 ALC:自动重启服务成功
2025/08/25 09:35:34.109 ALC:启动自动报警监控
2025/08/25 09:35:34.109 ALC:自动报警监控被关闭
2025/08/25 09:35:34.109 ALC:启动获取数据服务
2025/08/25 09:35:36.121 ALC:1#PLC点位开始轮询
2025/08/25 09:35:36.122 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:36.122 ALC:后台服务关闭
2025/08/25 09:35:36.123 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:37.133 ALC:获取数据服务停止
2025/08/25 09:35:44.121 ALC:后台服务启动成功
2025/08/25 09:35:44.135 ALC:自动重启服务成功
2025/08/25 09:35:44.136 ALC:启动自动报警监控
2025/08/25 09:35:44.137 ALC:自动报警监控被关闭
2025/08/25 09:35:44.138 ALC:启动获取数据服务
2025/08/25 09:35:46.132 ALC:1#PLC点位开始轮询
2025/08/25 09:35:46.133 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:46.134 ALC:后台服务关闭
2025/08/25 09:35:46.134 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:46.148 ALC:获取数据服务停止
2025/08/25 09:35:54.142 ALC:后台服务启动成功
2025/08/25 09:35:54.153 ALC:启动自动报警监控
2025/08/25 09:35:54.177 ALC:自动报警监控被关闭
2025/08/25 09:35:54.177 ALC:自动重启服务成功
2025/08/25 09:35:54.178 ALC:启动获取数据服务
2025/08/25 09:35:56.166 ALC:1#PLC点位开始轮询
2025/08/25 09:35:56.167 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:35:56.168 ALC:后台服务关闭
2025/08/25 09:35:56.169 ALC:1#PLC点位轮询被关闭
2025/08/25 09:35:56.196 ALC:获取数据服务停止
2025/08/25 09:36:04.181 ALC:后台服务启动成功
2025/08/25 09:36:04.190 ALC:自动重启服务成功
2025/08/25 09:36:04.191 ALC:启动自动报警监控
2025/08/25 09:36:04.192 ALC:自动报警监控被关闭
2025/08/25 09:36:04.193 ALC:启动获取数据服务
2025/08/25 09:36:06.196 ALC:1#PLC点位开始轮询
2025/08/25 09:36:06.197 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:36:06.197 ALC:后台服务关闭
2025/08/25 09:36:06.198 ALC:1#PLC点位轮询被关闭
2025/08/25 09:36:06.209 ALC:获取数据服务停止
2025/08/25 09:36:14.205 ALC:后台服务启动成功
2025/08/25 09:36:14.214 ALC:启动自动报警监控
2025/08/25 09:36:14.219 ALC:自动报警监控被关闭
2025/08/25 09:36:14.220 ALC:自动重启服务成功
2025/08/25 09:36:14.220 ALC:启动获取数据服务
2025/08/25 09:36:16.219 ALC:1#PLC点位开始轮询
2025/08/25 09:36:16.222 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:36:16.230 ALC:后台服务关闭
2025/08/25 09:36:16.246 ALC:1#PLC点位轮询被关闭
2025/08/25 09:36:16.281 ALC:获取数据服务停止
2025/08/25 09:36:24.238 ALC:后台服务启动成功
2025/08/25 09:36:24.253 ALC:自动重启服务成功
2025/08/25 09:36:24.254 ALC:启动自动报警监控
2025/08/25 09:36:24.254 ALC:自动报警监控被关闭
2025/08/25 09:36:24.255 ALC:启动获取数据服务
2025/08/25 09:36:26.248 ALC:1#PLC点位开始轮询
2025/08/25 09:36:26.248 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:36:26.249 ALC:后台服务关闭
2025/08/25 09:36:26.250 ALC:1#PLC点位轮询被关闭
2025/08/25 09:36:26.275 ALC:获取数据服务停止
2025/08/25 09:36:34.263 ALC:后台服务启动成功
2025/08/25 09:36:34.276 ALC:自动重启服务成功
2025/08/25 09:36:34.276 ALC:启动自动报警监控
2025/08/25 09:36:34.277 ALC:自动报警监控被关闭
2025/08/25 09:36:34.277 ALC:启动获取数据服务
2025/08/25 09:36:36.274 ALC:1#PLC点位开始轮询
2025/08/25 09:36:36.275 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:36:36.276 ALC:后台服务关闭
2025/08/25 09:36:36.276 ALC:1#PLC点位轮询被关闭
2025/08/25 09:36:36.305 ALC:获取数据服务停止
2025/08/25 09:36:44.291 ALC:后台服务启动成功
2025/08/25 09:36:44.299 ALC:启动自动报警监控
2025/08/25 09:36:44.326 ALC:自动报警监控被关闭
2025/08/25 09:36:44.327 ALC:自动重启服务成功
2025/08/25 09:36:44.328 ALC:启动获取数据服务
2025/08/25 09:36:46.301 ALC:1#PLC点位开始轮询
2025/08/25 09:36:46.301 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:36:46.302 ALC:后台服务关闭
2025/08/25 09:36:46.302 ALC:1#PLC点位轮询被关闭
2025/08/25 09:36:46.346 ALC:获取数据服务停止
