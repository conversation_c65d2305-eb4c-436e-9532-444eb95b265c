2025/08/25 08:55:56.370 开始加载插件。
2025/08/25 08:55:56.886 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.ALC.Service.dll'加载完成。
2025/08/25 08:55:57.504 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.ALC.www.dll'加载完成。
2025/08/25 08:55:58.374 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.Common.dll'加载完成。
2025/08/25 08:55:58.485 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.Cookie.dll'加载完成。
2025/08/25 08:55:59.269 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\dal\DAL.IFP.www.dll'加载完成。
2025/08/25 08:55:59.378 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.SignalR.dll'加载完成。
2025/08/25 08:55:59.554 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.WebSocket.dll'加载完成。
2025/08/25 08:55:59.632 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.ALC.www.dll'加载完成。
2025/08/25 08:56:00.020 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.Client.dll'加载完成。
2025/08/25 08:56:00.022 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.WebSocket.dll'加载完成。
2025/08/25 08:56:00.434 插件'E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\api\API.IFP.www.dll'加载完成。
2025/08/25 08:56:00.436 插件加载完成。
2025/08/25 08:56:00.554 启动websocket自动推送服务
2025/08/25 08:56:00.573 ALC:ALC数据提供者已初始化
2025/08/25 08:56:00.574 启动websocket自动推送服务守护线程
2025/08/25 08:56:00.576 ALC:ALC SignalR组件注册成功
2025/08/25 08:56:01.783 启动SignalR自动推送服务监控线程
2025/08/25 08:56:01.872 Now listening on: http://[::]:5009. 
2025/08/25 08:56:01.874 Application started. Press Ctrl+C to shut down. 
2025/08/25 08:56:01.875 Hosting environment: Production. 
2025/08/25 08:56:01.875 Content root path: E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\. 
2025/08/25 08:56:03.147 [ErrCode:E9999]未知错误|ALC:PLC连接失败
System.Exception: 连接************:502超时2s
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 112
   at DAL.ALC.Service.ServiceMain.Init() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 289
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:56:03.965 ALC:初始化步骤3，数据缓存器构建成功，点表长度391
2025/08/25 08:56:03.966 ALC:初始化步骤4，服务类初始化成功
2025/08/25 08:56:03.967 ALC:后台服务启动成功
2025/08/25 08:56:04.010 ALC:启动自动报警监控
2025/08/25 08:56:04.011 ALC:启动获取数据服务
2025/08/25 08:56:04.011 ALC:自动报警监控被关闭
2025/08/25 08:56:04.494 ALC:启动自动重启服务功能
2025/08/25 08:56:04.494 ALC:启动自动重连服务守护线程
2025/08/25 08:56:05.980 ALC:1#PLC点位开始轮询
2025/08/25 08:56:05.980 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:05.981 ALC:后台服务关闭
2025/08/25 08:56:05.982 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:06.029 ALC:获取数据服务停止
2025/08/25 08:56:14.500 ALC:后台服务启动成功
2025/08/25 08:56:14.511 ALC:启动自动报警监控
2025/08/25 08:56:14.546 ALC:自动报警监控被关闭
2025/08/25 08:56:14.547 ALC:自动重启服务成功
2025/08/25 08:56:14.547 ALC:启动获取数据服务
2025/08/25 08:56:16.523 ALC:1#PLC点位开始轮询
2025/08/25 08:56:16.523 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:16.524 ALC:后台服务关闭
2025/08/25 08:56:16.524 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:16.558 ALC:获取数据服务停止
2025/08/25 08:56:24.562 ALC:后台服务启动成功
2025/08/25 08:56:24.571 ALC:启动自动报警监控
2025/08/25 08:56:24.574 ALC:自动报警监控被关闭
2025/08/25 08:56:24.574 ALC:启动获取数据服务
2025/08/25 08:56:24.575 ALC:自动重启服务成功
2025/08/25 08:56:26.571 ALC:1#PLC点位开始轮询
2025/08/25 08:56:26.572 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:26.573 ALC:后台服务关闭
2025/08/25 08:56:26.573 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:26.587 ALC:获取数据服务停止
2025/08/25 08:56:33.662 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 08:56:33.781 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 117.7890ms. 
2025/08/25 08:56:34.243 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 08:56:34.253 CORS policy execution successful. 
2025/08/25 08:56:34.257 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 14.1519ms. 
2025/08/25 08:56:34.261 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 08:56:34.262 CORS policy execution successful. 
2025/08/25 08:56:34.265 Executing endpoint '/socket/negotiate'. 
2025/08/25 08:56:34.302 Executed endpoint '/socket/negotiate'. 
2025/08/25 08:56:34.303 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 42.0943ms. 
2025/08/25 08:56:34.309 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=l9EIlcOitSrXyL8-SZzQsw - -. 
2025/08/25 08:56:34.310 CORS policy execution successful. 
2025/08/25 08:56:34.310 Executing endpoint '/socket'. 
2025/08/25 08:56:34.581 ALC:后台服务启动成功
2025/08/25 08:56:34.593 ALC:启动自动报警监控
2025/08/25 08:56:34.595 ALC:自动重启服务成功
2025/08/25 08:56:34.596 ALC:自动报警监控被关闭
2025/08/25 08:56:34.596 ALC:启动获取数据服务
2025/08/25 08:56:36.590 ALC:1#PLC点位开始轮询
2025/08/25 08:56:36.590 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:36.591 ALC:后台服务关闭
2025/08/25 08:56:36.592 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:36.620 ALC:获取数据服务停止
2025/08/25 08:56:44.609 ALC:后台服务启动成功
2025/08/25 08:56:44.619 ALC:自动重启服务成功
2025/08/25 08:56:44.620 ALC:启动自动报警监控
2025/08/25 08:56:44.621 ALC:启动获取数据服务
2025/08/25 08:56:44.622 ALC:自动报警监控被关闭
2025/08/25 08:56:46.014 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/User/Login application/json 84. 
2025/08/25 08:56:46.015 CORS policy execution successful. 
2025/08/25 08:56:46.260 查询到用户:1
2025/08/25 08:56:46.380 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/User/Login application/json 84 - 200 - application/json;+charset=utf-8 365.2787ms. 
2025/08/25 08:56:46.622 ALC:1#PLC点位开始轮询
2025/08/25 08:56:46.622 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:46.623 ALC:后台服务关闭
2025/08/25 08:56:46.624 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:46.637 ALC:获取数据服务停止
2025/08/25 08:56:46.705 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/UBAC/GetUserEffectivePermissions application/json 19. 
2025/08/25 08:56:46.705 CORS policy execution successful. 
2025/08/25 08:56:46.774 用户角色为 System.Collections.Generic.List`1[COM.IFP.SqlSugarN.Field`1[System.String]]
2025/08/25 08:56:46.972 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/UBAC/GetUserEffectivePermissions application/json 19 - 200 - application/json;+charset=utf-8 267.4465ms. 
2025/08/25 08:56:46.986 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 08:56:46.987 CORS policy execution successful. 
2025/08/25 08:56:47.054 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 08:56:47.258 用户 admin 是超级管理员，返回所有菜单
2025/08/25 08:56:47.296 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 08:56:47.304 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 317.5826ms. 
2025/08/25 08:56:54.633 ALC:后台服务启动成功
2025/08/25 08:56:54.644 ALC:自动重启服务成功
2025/08/25 08:56:54.644 ALC:启动自动报警监控
2025/08/25 08:56:54.645 ALC:自动报警监控被关闭
2025/08/25 08:56:54.645 ALC:启动获取数据服务
2025/08/25 08:56:56.650 ALC:1#PLC点位开始轮询
2025/08/25 08:56:56.651 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:56:56.652 ALC:后台服务关闭
2025/08/25 08:56:56.653 ALC:1#PLC点位轮询被关闭
2025/08/25 08:56:57.659 ALC:获取数据服务停止
2025/08/25 08:57:05.712 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 你的主机中的软件中止了一个已建立的连接。.
 ---> System.Net.Sockets.SocketException (10053): 你的主机中的软件中止了一个已建立的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 124
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 08:57:05.717 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: Unable to write data to the transport connection: 你的主机中的软件中止了一个已建立的连接。.
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:57:15.765 ALC:后台服务启动成功
2025/08/25 08:57:15.775 ALC:启动自动报警监控
2025/08/25 08:57:15.803 ALC:自动报警监控被关闭
2025/08/25 08:57:15.804 ALC:自动重启服务成功
2025/08/25 08:57:15.804 ALC:启动获取数据服务
2025/08/25 08:57:17.777 ALC:1#PLC点位开始轮询
2025/08/25 08:57:17.778 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:17.779 ALC:后台服务关闭
2025/08/25 08:57:17.779 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:17.842 ALC:获取数据服务停止
2025/08/25 08:57:25.821 ALC:后台服务启动成功
2025/08/25 08:57:25.833 ALC:自动重启服务成功
2025/08/25 08:57:25.834 ALC:启动自动报警监控
2025/08/25 08:57:25.834 ALC:自动报警监控被关闭
2025/08/25 08:57:25.835 ALC:启动获取数据服务
2025/08/25 08:57:27.829 ALC:1#PLC点位开始轮询
2025/08/25 08:57:27.830 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:27.832 ALC:后台服务关闭
2025/08/25 08:57:27.832 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:27.847 ALC:获取数据服务停止
2025/08/25 08:57:36.081 ALC:后台服务启动成功
2025/08/25 08:57:36.100 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:57:36.107 ALC:自动重启服务成功
2025/08/25 08:57:36.108 ALC:启动自动报警监控
2025/08/25 08:57:36.108 ALC:自动报警监控被关闭
2025/08/25 08:57:36.109 ALC:启动获取数据服务
2025/08/25 08:57:38.102 ALC:1#PLC点位开始轮询
2025/08/25 08:57:38.103 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:38.104 ALC:后台服务关闭
2025/08/25 08:57:38.104 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:38.120 ALC:获取数据服务停止
2025/08/25 08:57:46.117 ALC:后台服务启动成功
2025/08/25 08:57:46.126 ALC:启动自动报警监控
2025/08/25 08:57:46.130 ALC:自动报警监控被关闭
2025/08/25 08:57:46.130 ALC:自动重启服务成功
2025/08/25 08:57:46.131 ALC:启动获取数据服务
2025/08/25 08:57:48.134 ALC:1#PLC点位开始轮询
2025/08/25 08:57:48.135 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:48.135 ALC:后台服务关闭
2025/08/25 08:57:48.135 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:48.162 ALC:获取数据服务停止
2025/08/25 08:57:56.136 ALC:后台服务启动成功
2025/08/25 08:57:56.146 ALC:自动重启服务成功
2025/08/25 08:57:56.147 ALC:启动自动报警监控
2025/08/25 08:57:56.147 ALC:自动报警监控被关闭
2025/08/25 08:57:56.147 ALC:启动获取数据服务
2025/08/25 08:57:58.155 ALC:1#PLC点位开始轮询
2025/08/25 08:57:58.160 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:57:58.161 ALC:后台服务关闭
2025/08/25 08:57:58.162 ALC:1#PLC点位轮询被关闭
2025/08/25 08:57:59.169 ALC:获取数据服务停止
2025/08/25 08:58:06.156 ALC:后台服务启动成功
2025/08/25 08:58:06.170 ALC:自动重启服务成功
2025/08/25 08:58:06.171 ALC:启动自动报警监控
2025/08/25 08:58:06.171 ALC:自动报警监控被关闭
2025/08/25 08:58:06.172 ALC:启动获取数据服务
2025/08/25 08:58:10.602 ALC:1#PLC点位开始轮询
2025/08/25 08:58:10.618 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:10.628 ALC:后台服务关闭
2025/08/25 08:58:10.628 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:10.633 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:58:11.599 ALC:获取数据服务停止
2025/08/25 08:58:16.182 ALC:后台服务启动成功
2025/08/25 08:58:16.193 ALC:启动自动报警监控
2025/08/25 08:58:16.229 ALC:自动报警监控被关闭
2025/08/25 08:58:16.229 ALC:自动重启服务成功
2025/08/25 08:58:16.230 ALC:启动获取数据服务
2025/08/25 08:58:18.197 ALC:1#PLC点位开始轮询
2025/08/25 08:58:18.198 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:18.198 ALC:后台服务关闭
2025/08/25 08:58:18.199 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:18.248 ALC:获取数据服务停止
2025/08/25 08:58:26.239 ALC:后台服务启动成功
2025/08/25 08:58:26.249 ALC:自动重启服务成功
2025/08/25 08:58:26.250 ALC:启动自动报警监控
2025/08/25 08:58:26.250 ALC:自动报警监控被关闭
2025/08/25 08:58:26.250 ALC:启动获取数据服务
2025/08/25 08:58:28.250 ALC:1#PLC点位开始轮询
2025/08/25 08:58:28.251 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:28.255 ALC:后台服务关闭
2025/08/25 08:58:28.256 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:28.260 ALC:获取数据服务停止
2025/08/25 08:58:36.255 ALC:后台服务启动成功
2025/08/25 08:58:36.266 ALC:自动重启服务成功
2025/08/25 08:58:36.267 ALC:启动自动报警监控
2025/08/25 08:58:36.267 ALC:自动报警监控被关闭
2025/08/25 08:58:36.268 ALC:启动获取数据服务
2025/08/25 08:58:38.262 ALC:1#PLC点位开始轮询
2025/08/25 08:58:38.263 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:38.264 ALC:后台服务关闭
2025/08/25 08:58:38.264 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:38.278 ALC:获取数据服务停止
2025/08/25 08:58:46.274 ALC:后台服务启动成功
2025/08/25 08:58:46.286 ALC:自动重启服务成功
2025/08/25 08:58:46.286 ALC:启动自动报警监控
2025/08/25 08:58:46.287 ALC:自动报警监控被关闭
2025/08/25 08:58:46.287 ALC:启动获取数据服务
2025/08/25 08:58:48.297 ALC:1#PLC点位开始轮询
2025/08/25 08:58:48.298 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:48.298 ALC:后台服务关闭
2025/08/25 08:58:48.299 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:49.300 ALC:获取数据服务停止
2025/08/25 08:58:56.291 ALC:后台服务启动成功
2025/08/25 08:58:56.300 ALC:启动自动报警监控
2025/08/25 08:58:56.302 ALC:自动报警监控被关闭
2025/08/25 08:58:56.303 ALC:启动获取数据服务
2025/08/25 08:58:56.303 ALC:自动重启服务成功
2025/08/25 08:58:58.306 ALC:1#PLC点位开始轮询
2025/08/25 08:58:58.307 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:58:58.307 ALC:后台服务关闭
2025/08/25 08:58:58.307 ALC:1#PLC点位轮询被关闭
2025/08/25 08:58:58.318 ALC:获取数据服务停止
2025/08/25 08:59:06.310 ALC:后台服务启动成功
2025/08/25 08:59:06.321 ALC:自动重启服务成功
2025/08/25 08:59:06.322 ALC:启动自动报警监控
2025/08/25 08:59:06.322 ALC:自动报警监控被关闭
2025/08/25 08:59:06.323 ALC:启动获取数据服务
2025/08/25 08:59:08.325 ALC:1#PLC点位开始轮询
2025/08/25 08:59:08.328 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:08.329 ALC:后台服务关闭
2025/08/25 08:59:08.329 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:08.340 ALC:获取数据服务停止
2025/08/25 08:59:16.324 ALC:后台服务启动成功
2025/08/25 08:59:16.337 ALC:自动重启服务成功
2025/08/25 08:59:16.338 ALC:启动自动报警监控
2025/08/25 08:59:16.338 ALC:自动报警监控被关闭
2025/08/25 08:59:16.339 ALC:启动获取数据服务
2025/08/25 08:59:18.343 ALC:1#PLC点位开始轮询
2025/08/25 08:59:18.344 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:18.345 ALC:后台服务关闭
2025/08/25 08:59:18.345 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:19.345 ALC:获取数据服务停止
2025/08/25 08:59:26.351 ALC:后台服务启动成功
2025/08/25 08:59:26.359 ALC:启动自动报警监控
2025/08/25 08:59:26.387 ALC:自动报警监控被关闭
2025/08/25 08:59:26.388 ALC:自动重启服务成功
2025/08/25 08:59:26.388 ALC:启动获取数据服务
2025/08/25 08:59:28.371 ALC:1#PLC点位开始轮询
2025/08/25 08:59:28.371 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:28.372 ALC:后台服务关闭
2025/08/25 08:59:28.372 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:28.403 ALC:获取数据服务停止
2025/08/25 08:59:36.400 ALC:后台服务启动成功
2025/08/25 08:59:36.408 ALC:启动自动报警监控
2025/08/25 08:59:36.441 ALC:自动报警监控被关闭
2025/08/25 08:59:36.441 ALC:自动重启服务成功
2025/08/25 08:59:36.442 ALC:启动获取数据服务
2025/08/25 08:59:38.165 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:59:38.409 ALC:1#PLC点位开始轮询
2025/08/25 08:59:38.409 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:38.410 ALC:后台服务关闭
2025/08/25 08:59:38.411 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:39.153 ALC:获取数据服务停止
2025/08/25 08:59:46.460 ALC:后台服务启动成功
2025/08/25 08:59:46.470 ALC:自动重启服务成功
2025/08/25 08:59:46.471 ALC:启动自动报警监控
2025/08/25 08:59:46.471 ALC:自动报警监控被关闭
2025/08/25 08:59:46.472 ALC:启动获取数据服务
2025/08/25 08:59:48.480 ALC:1#PLC点位开始轮询
2025/08/25 08:59:48.481 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:48.481 ALC:后台服务关闭
2025/08/25 08:59:48.482 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:48.483 ALC:获取数据服务停止
2025/08/25 08:59:51.861 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 08:59:56.838 ALC:后台服务启动成功
2025/08/25 08:59:56.848 ALC:自动重启服务成功
2025/08/25 08:59:56.849 ALC:启动自动报警监控
2025/08/25 08:59:56.849 ALC:自动报警监控被关闭
2025/08/25 08:59:56.849 ALC:启动获取数据服务
2025/08/25 08:59:58.859 ALC:1#PLC点位开始轮询
2025/08/25 08:59:58.860 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 08:59:58.861 ALC:后台服务关闭
2025/08/25 08:59:58.862 ALC:1#PLC点位轮询被关闭
2025/08/25 08:59:58.872 ALC:获取数据服务停止
2025/08/25 09:00:08.071 ALC:后台服务启动成功
2025/08/25 09:00:08.094 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:00:08.096 ALC:自动重启服务成功
2025/08/25 09:00:08.097 ALC:启动自动报警监控
2025/08/25 09:00:08.097 ALC:自动报警监控被关闭
2025/08/25 09:00:08.098 ALC:启动获取数据服务
2025/08/25 09:00:10.097 ALC:1#PLC点位开始轮询
2025/08/25 09:00:10.098 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:10.099 ALC:后台服务关闭
2025/08/25 09:00:10.100 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:10.106 ALC:获取数据服务停止
2025/08/25 09:00:18.109 ALC:后台服务启动成功
2025/08/25 09:00:18.155 ALC:自动重启服务成功
2025/08/25 09:00:18.184 ALC:启动自动报警监控
2025/08/25 09:00:18.191 ALC:自动报警监控被关闭
2025/08/25 09:00:18.193 ALC:启动获取数据服务
2025/08/25 09:00:20.147 ALC:1#PLC点位开始轮询
2025/08/25 09:00:20.148 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:20.149 ALC:后台服务关闭
2025/08/25 09:00:20.149 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:20.211 ALC:获取数据服务停止
2025/08/25 09:00:28.201 ALC:后台服务启动成功
2025/08/25 09:00:28.227 ALC:启动自动报警监控
2025/08/25 09:00:28.231 ALC:自动报警监控被关闭
2025/08/25 09:00:28.233 ALC:自动重启服务成功
2025/08/25 09:00:28.233 ALC:启动获取数据服务
2025/08/25 09:00:30.232 ALC:1#PLC点位开始轮询
2025/08/25 09:00:30.294 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:30.362 ALC:后台服务关闭
2025/08/25 09:00:30.363 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:31.307 ALC:获取数据服务停止
2025/08/25 09:00:38.245 ALC:后台服务启动成功
2025/08/25 09:00:38.281 ALC:启动自动报警监控
2025/08/25 09:00:38.284 ALC:自动报警监控被关闭
2025/08/25 09:00:38.287 ALC:自动重启服务成功
2025/08/25 09:00:38.288 ALC:启动获取数据服务
2025/08/25 09:00:40.287 ALC:1#PLC点位开始轮询
2025/08/25 09:00:40.292 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:00:40.294 ALC:后台服务关闭
2025/08/25 09:00:40.296 ALC:1#PLC点位轮询被关闭
2025/08/25 09:00:40.383 ALC:获取数据服务停止
2025/08/25 09:00:49.094 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:00:59.216 ALC:后台服务启动成功
2025/08/25 09:01:00.457 ALC:启动自动报警监控
2025/08/25 09:01:02.285 ALC:自动重启服务成功
2025/08/25 09:01:02.654 ALC:自动报警监控被关闭
2025/08/25 09:01:02.655 ALC:1#PLC点位开始轮询
2025/08/25 09:01:02.656 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:02.657 ALC:后台服务关闭
2025/08/25 09:01:02.658 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:02.803 ALC:启动获取数据服务
2025/08/25 09:01:02.820 ALC:获取数据服务停止
2025/08/25 09:01:12.684 ALC:后台服务启动成功
2025/08/25 09:01:12.713 ALC:自动重启服务成功
2025/08/25 09:01:12.714 ALC:启动自动报警监控
2025/08/25 09:01:12.716 ALC:自动报警监控被关闭
2025/08/25 09:01:12.717 ALC:启动获取数据服务
2025/08/25 09:01:14.702 ALC:1#PLC点位开始轮询
2025/08/25 09:01:14.703 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:14.704 ALC:后台服务关闭
2025/08/25 09:01:14.705 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:14.747 ALC:获取数据服务停止
2025/08/25 09:01:22.729 ALC:后台服务启动成功
2025/08/25 09:01:22.816 ALC:启动自动报警监控
2025/08/25 09:01:22.821 ALC:自动报警监控被关闭
2025/08/25 09:01:22.825 ALC:自动重启服务成功
2025/08/25 09:01:22.827 ALC:启动获取数据服务
2025/08/25 09:01:24.793 ALC:1#PLC点位开始轮询
2025/08/25 09:01:24.794 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:24.795 ALC:后台服务关闭
2025/08/25 09:01:24.796 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:24.854 ALC:获取数据服务停止
2025/08/25 09:01:32.842 ALC:后台服务启动成功
2025/08/25 09:01:32.859 ALC:启动自动报警监控
2025/08/25 09:01:32.860 ALC:自动报警监控被关闭
2025/08/25 09:01:32.868 ALC:自动重启服务成功
2025/08/25 09:01:32.869 ALC:启动获取数据服务
2025/08/25 09:01:34.854 ALC:1#PLC点位开始轮询
2025/08/25 09:01:34.855 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:34.856 ALC:后台服务关闭
2025/08/25 09:01:34.857 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:34.885 ALC:获取数据服务停止
2025/08/25 09:01:42.964 ALC:后台服务启动成功
2025/08/25 09:01:43.111 ALC:自动重启服务成功
2025/08/25 09:01:43.112 ALC:启动自动报警监控
2025/08/25 09:01:43.113 ALC:自动报警监控被关闭
2025/08/25 09:01:43.113 ALC:启动获取数据服务
2025/08/25 09:01:45.099 ALC:1#PLC点位开始轮询
2025/08/25 09:01:45.101 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:45.122 ALC:后台服务关闭
2025/08/25 09:01:45.128 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:45.141 ALC:获取数据服务停止
2025/08/25 09:01:53.171 ALC:后台服务启动成功
2025/08/25 09:01:53.282 ALC:自动重启服务成功
2025/08/25 09:01:53.772 ALC:启动获取数据服务
2025/08/25 09:01:53.773 ALC:启动自动报警监控
2025/08/25 09:01:53.774 ALC:自动报警监控被关闭
2025/08/25 09:01:55.784 ALC:1#PLC点位开始轮询
2025/08/25 09:01:55.796 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:01:55.797 ALC:后台服务关闭
2025/08/25 09:01:55.798 ALC:1#PLC点位轮询被关闭
2025/08/25 09:01:56.939 ALC:获取数据服务停止
2025/08/25 09:02:03.369 ALC:后台服务启动成功
2025/08/25 09:02:03.392 ALC:自动重启服务成功
2025/08/25 09:02:03.393 ALC:启动自动报警监控
2025/08/25 09:02:03.394 ALC:自动报警监控被关闭
2025/08/25 09:02:03.400 ALC:启动获取数据服务
2025/08/25 09:02:05.395 ALC:1#PLC点位开始轮询
2025/08/25 09:02:05.472 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:05.635 ALC:后台服务关闭
2025/08/25 09:02:05.716 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:06.448 ALC:获取数据服务停止
2025/08/25 09:02:13.420 ALC:后台服务启动成功
2025/08/25 09:02:13.447 ALC:启动自动报警监控
2025/08/25 09:02:13.447 ALC:自动报警监控被关闭
2025/08/25 09:02:13.458 ALC:自动重启服务成功
2025/08/25 09:02:13.459 ALC:启动获取数据服务
2025/08/25 09:02:15.450 ALC:1#PLC点位开始轮询
2025/08/25 09:02:15.450 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:15.451 ALC:后台服务关闭
2025/08/25 09:02:15.453 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:15.482 ALC:获取数据服务停止
2025/08/25 09:02:23.516 ALC:后台服务启动成功
2025/08/25 09:02:23.715 ALC:启动自动报警监控
2025/08/25 09:02:23.715 ALC:自动报警监控被关闭
2025/08/25 09:02:23.733 ALC:自动重启服务成功
2025/08/25 09:02:23.753 ALC:启动获取数据服务
2025/08/25 09:02:25.707 ALC:1#PLC点位开始轮询
2025/08/25 09:02:25.708 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:25.709 ALC:后台服务关闭
2025/08/25 09:02:25.709 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:25.769 ALC:获取数据服务停止
2025/08/25 09:02:33.786 ALC:后台服务启动成功
2025/08/25 09:02:33.905 ALC:启动自动报警监控
2025/08/25 09:02:33.905 ALC:自动报警监控被关闭
2025/08/25 09:02:33.914 ALC:自动重启服务成功
2025/08/25 09:02:33.919 ALC:启动获取数据服务
2025/08/25 09:02:35.882 ALC:1#PLC点位开始轮询
2025/08/25 09:02:35.884 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:35.886 ALC:后台服务关闭
2025/08/25 09:02:35.889 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:35.944 ALC:获取数据服务停止
2025/08/25 09:02:43.927 ALC:后台服务启动成功
2025/08/25 09:02:43.939 ALC:自动重启服务成功
2025/08/25 09:02:43.940 ALC:启动自动报警监控
2025/08/25 09:02:43.941 ALC:自动报警监控被关闭
2025/08/25 09:02:43.941 ALC:启动获取数据服务
2025/08/25 09:02:45.938 ALC:1#PLC点位开始轮询
2025/08/25 09:02:45.938 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:45.939 ALC:后台服务关闭
2025/08/25 09:02:45.939 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:45.953 ALC:获取数据服务停止
2025/08/25 09:02:53.945 ALC:后台服务启动成功
2025/08/25 09:02:53.954 ALC:启动自动报警监控
2025/08/25 09:02:53.956 ALC:自动报警监控被关闭
2025/08/25 09:02:53.957 ALC:自动重启服务成功
2025/08/25 09:02:53.957 ALC:启动获取数据服务
2025/08/25 09:02:55.954 ALC:1#PLC点位开始轮询
2025/08/25 09:02:55.955 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:02:55.956 ALC:后台服务关闭
2025/08/25 09:02:55.956 ALC:1#PLC点位轮询被关闭
2025/08/25 09:02:55.986 ALC:获取数据服务停止
2025/08/25 09:03:03.971 ALC:后台服务启动成功
2025/08/25 09:03:03.980 ALC:自动重启服务成功
2025/08/25 09:03:03.980 ALC:启动获取数据服务
2025/08/25 09:03:03.981 ALC:启动自动报警监控
2025/08/25 09:03:03.982 ALC:自动报警监控被关闭
2025/08/25 09:03:05.989 ALC:1#PLC点位开始轮询
2025/08/25 09:03:05.989 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:05.990 ALC:后台服务关闭
2025/08/25 09:03:05.991 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:07.005 ALC:获取数据服务停止
2025/08/25 09:03:13.990 ALC:后台服务启动成功
2025/08/25 09:03:14.002 ALC:启动自动报警监控
2025/08/25 09:03:14.039 ALC:自动报警监控被关闭
2025/08/25 09:03:14.040 ALC:自动重启服务成功
2025/08/25 09:03:14.040 ALC:启动获取数据服务
2025/08/25 09:03:16.000 ALC:1#PLC点位开始轮询
2025/08/25 09:03:16.001 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:16.002 ALC:后台服务关闭
2025/08/25 09:03:16.003 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:16.063 ALC:获取数据服务停止
2025/08/25 09:03:24.046 ALC:后台服务启动成功
2025/08/25 09:03:24.070 ALC:自动重启服务成功
2025/08/25 09:03:24.071 ALC:启动自动报警监控
2025/08/25 09:03:24.072 ALC:自动报警监控被关闭
2025/08/25 09:03:24.073 ALC:启动获取数据服务
2025/08/25 09:03:26.064 ALC:1#PLC点位开始轮询
2025/08/25 09:03:26.066 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:26.067 ALC:后台服务关闭
2025/08/25 09:03:26.068 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:26.084 ALC:获取数据服务停止
2025/08/25 09:03:34.089 ALC:后台服务启动成功
2025/08/25 09:03:34.104 ALC:启动自动报警监控
2025/08/25 09:03:34.105 ALC:自动报警监控被关闭
2025/08/25 09:03:34.109 ALC:自动重启服务成功
2025/08/25 09:03:34.110 ALC:启动获取数据服务
2025/08/25 09:03:36.101 ALC:1#PLC点位开始轮询
2025/08/25 09:03:36.102 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:36.103 ALC:后台服务关闭
2025/08/25 09:03:36.104 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:36.133 ALC:获取数据服务停止
2025/08/25 09:03:39.614 Executed endpoint '/socket'. 
2025/08/25 09:03:39.614 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=l9EIlcOitSrXyL8-SZzQsw - - - 101 - - 425305.1160ms. 
2025/08/25 09:03:40.986 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:40.988 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.8477ms. 
2025/08/25 09:03:41.206 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:41.207 CORS policy execution successful. 
2025/08/25 09:03:41.210 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:41.251 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:41.267 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:41.268 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 61.3888ms. 
2025/08/25 09:03:42.334 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:42.336 CORS policy execution successful. 
2025/08/25 09:03:42.336 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.9515ms. 
2025/08/25 09:03:42.347 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:42.348 CORS policy execution successful. 
2025/08/25 09:03:42.348 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:42.349 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:42.350 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 3.2582ms. 
2025/08/25 09:03:42.580 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=ppc8TZwqQBBNbq_yfh3VJg - -. 
2025/08/25 09:03:42.581 CORS policy execution successful. 
2025/08/25 09:03:42.587 Executing endpoint '/socket'. 
2025/08/25 09:03:44.188 ALC:后台服务启动成功
2025/08/25 09:03:44.205 ALC:自动重启服务成功
2025/08/25 09:03:44.206 ALC:启动自动报警监控
2025/08/25 09:03:44.206 ALC:自动报警监控被关闭
2025/08/25 09:03:44.207 ALC:启动获取数据服务
2025/08/25 09:03:46.212 ALC:1#PLC点位开始轮询
2025/08/25 09:03:46.212 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:46.214 ALC:后台服务关闭
2025/08/25 09:03:46.217 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:46.220 ALC:获取数据服务停止
2025/08/25 09:03:47.468 Executed endpoint '/socket'. 
2025/08/25 09:03:47.480 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=ppc8TZwqQBBNbq_yfh3VJg - - - 101 - - 4899.9960ms. 
2025/08/25 09:03:49.100 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:49.101 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.9091ms. 
2025/08/25 09:03:49.187 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:49.322 CORS policy execution successful. 
2025/08/25 09:03:49.330 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:49.468 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:49.527 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:49.556 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 368.9094ms. 
2025/08/25 09:03:50.207 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:50.207 CORS policy execution successful. 
2025/08/25 09:03:50.208 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.9608ms. 
2025/08/25 09:03:50.210 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:50.211 CORS policy execution successful. 
2025/08/25 09:03:50.211 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:50.211 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:50.212 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.4374ms. 
2025/08/25 09:03:50.378 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=qXYKFQ4i4cDI3w3YEfacRQ - -. 
2025/08/25 09:03:50.378 CORS policy execution successful. 
2025/08/25 09:03:50.378 Executing endpoint '/socket'. 
2025/08/25 09:03:54.518 ALC:后台服务启动成功
2025/08/25 09:03:54.533 ALC:自动重启服务成功
2025/08/25 09:03:54.534 ALC:启动自动报警监控
2025/08/25 09:03:54.534 ALC:自动报警监控被关闭
2025/08/25 09:03:54.535 ALC:启动获取数据服务
2025/08/25 09:03:56.535 ALC:1#PLC点位开始轮询
2025/08/25 09:03:56.536 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:03:56.536 ALC:后台服务关闭
2025/08/25 09:03:56.537 ALC:1#PLC点位轮询被关闭
2025/08/25 09:03:56.553 ALC:获取数据服务停止
2025/08/25 09:03:58.567 Executed endpoint '/socket'. 
2025/08/25 09:03:58.575 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=qXYKFQ4i4cDI3w3YEfacRQ - - - 101 - - 8197.4143ms. 
2025/08/25 09:03:59.355 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:03:59.360 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 4.3551ms. 
2025/08/25 09:03:59.415 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:03:59.417 CORS policy execution successful. 
2025/08/25 09:03:59.425 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:03:59.431 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:03:59.436 CORS policy execution successful. 
2025/08/25 09:03:59.436 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 5.6383ms. 
2025/08/25 09:03:59.443 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:03:59.445 CORS policy execution successful. 
2025/08/25 09:03:59.446 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:03:59.446 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:03:59.447 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 4.0172ms. 
2025/08/25 09:03:59.455 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=_GWYdp8lZYNIOXgUzvmpVA - -. 
2025/08/25 09:03:59.455 CORS policy execution successful. 
2025/08/25 09:03:59.462 Executing endpoint '/socket'. 
2025/08/25 09:03:59.462 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:03:59.481 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:03:59.482 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 66.7060ms. 
2025/08/25 09:04:04.742 ALC:后台服务启动成功
2025/08/25 09:04:04.756 ALC:启动自动报警监控
2025/08/25 09:04:04.760 ALC:自动报警监控被关闭
2025/08/25 09:04:04.760 ALC:自动重启服务成功
2025/08/25 09:04:04.761 ALC:启动获取数据服务
2025/08/25 09:04:06.765 ALC:1#PLC点位开始轮询
2025/08/25 09:04:06.766 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:06.766 ALC:后台服务关闭
2025/08/25 09:04:06.767 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:06.777 ALC:获取数据服务停止
2025/08/25 09:04:08.380 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:08.381 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 0.6434ms. 
2025/08/25 09:04:08.444 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:08.445 CORS policy execution successful. 
2025/08/25 09:04:08.448 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:08.454 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:08.454 CORS policy execution successful. 
2025/08/25 09:04:08.456 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.7627ms. 
2025/08/25 09:04:08.462 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:08.463 CORS policy execution successful. 
2025/08/25 09:04:08.463 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:08.464 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:08.464 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 1.9831ms. 
2025/08/25 09:04:08.466 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:08.481 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:08.482 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 37.8165ms. 
2025/08/25 09:04:08.485 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=M83jhn7Jk9TdrkE80u2G5Q - -. 
2025/08/25 09:04:08.485 CORS policy execution successful. 
2025/08/25 09:04:08.485 Executing endpoint '/socket'. 
2025/08/25 09:04:15.403 ALC:后台服务启动成功
2025/08/25 09:04:15.437 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:04:15.442 ALC:自动重启服务成功
2025/08/25 09:04:15.443 ALC:启动自动报警监控
2025/08/25 09:04:15.443 ALC:自动报警监控被关闭
2025/08/25 09:04:15.444 ALC:启动获取数据服务
2025/08/25 09:04:15.467 Executed endpoint '/socket'. 
2025/08/25 09:04:15.467 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=_GWYdp8lZYNIOXgUzvmpVA - - - 101 - - 16012.2765ms. 
2025/08/25 09:04:17.434 ALC:1#PLC点位开始轮询
2025/08/25 09:04:17.435 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:17.436 ALC:后台服务关闭
2025/08/25 09:04:17.436 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:17.453 ALC:获取数据服务停止
2025/08/25 09:04:18.360 Executed endpoint '/socket'. 
2025/08/25 09:04:18.364 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=M83jhn7Jk9TdrkE80u2G5Q - - - 101 - - 9878.5429ms. 
2025/08/25 09:04:19.675 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:19.676 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.2212ms. 
2025/08/25 09:04:19.757 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:19.759 CORS policy execution successful. 
2025/08/25 09:04:19.760 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:19.761 CORS policy execution successful. 
2025/08/25 09:04:19.761 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 0.8417ms. 
2025/08/25 09:04:19.764 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:19.766 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:19.772 CORS policy execution successful. 
2025/08/25 09:04:19.774 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:19.775 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:19.780 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 13.9621ms. 
2025/08/25 09:04:19.784 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:19.813 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=HiZdFmyuGr4IPM4-bp5YtQ - -. 
2025/08/25 09:04:19.815 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:19.818 CORS policy execution successful. 
2025/08/25 09:04:19.819 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 61.4407ms. 
2025/08/25 09:04:19.819 Executing endpoint '/socket'. 
2025/08/25 09:04:25.540 ALC:后台服务启动成功
2025/08/25 09:04:25.551 ALC:自动重启服务成功
2025/08/25 09:04:25.552 ALC:启动自动报警监控
2025/08/25 09:04:25.552 ALC:自动报警监控被关闭
2025/08/25 09:04:25.553 ALC:启动获取数据服务
2025/08/25 09:04:27.555 ALC:1#PLC点位开始轮询
2025/08/25 09:04:27.558 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:27.559 ALC:后台服务关闭
2025/08/25 09:04:27.560 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:27.570 ALC:获取数据服务停止
2025/08/25 09:04:35.566 ALC:后台服务启动成功
2025/08/25 09:04:35.577 ALC:自动重启服务成功
2025/08/25 09:04:35.578 ALC:启动获取数据服务
2025/08/25 09:04:35.578 ALC:启动自动报警监控
2025/08/25 09:04:35.578 ALC:自动报警监控被关闭
2025/08/25 09:04:36.414 Executed endpoint '/socket'. 
2025/08/25 09:04:36.421 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=HiZdFmyuGr4IPM4-bp5YtQ - - - 101 - - 16607.6176ms. 
2025/08/25 09:04:37.490 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:37.542 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 52.0583ms. 
2025/08/25 09:04:37.606 ALC:1#PLC点位开始轮询
2025/08/25 09:04:37.695 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:37.708 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:37.708 ALC:后台服务关闭
2025/08/25 09:04:37.710 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:37.730 CORS policy execution successful. 
2025/08/25 09:04:37.730 CORS policy execution successful. 
2025/08/25 09:04:37.731 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 22.4000ms. 
2025/08/25 09:04:37.730 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:37.791 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:37.791 CORS policy execution successful. 
2025/08/25 09:04:37.791 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:37.792 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:37.792 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:37.793 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.2966ms. 
2025/08/25 09:04:37.806 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=rIDOa_0olxvEN7BMZgLeRw - -. 
2025/08/25 09:04:37.813 CORS policy execution successful. 
2025/08/25 09:04:37.815 Executing endpoint '/socket'. 
2025/08/25 09:04:37.828 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:37.865 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:37.882 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 174.4951ms. 
2025/08/25 09:04:38.674 ALC:获取数据服务停止
2025/08/25 09:04:45.585 ALC:后台服务启动成功
2025/08/25 09:04:45.599 ALC:自动重启服务成功
2025/08/25 09:04:45.601 ALC:启动自动报警监控
2025/08/25 09:04:45.602 ALC:自动报警监控被关闭
2025/08/25 09:04:45.604 ALC:启动获取数据服务
2025/08/25 09:04:47.562 Executed endpoint '/socket'. 
2025/08/25 09:04:47.562 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=rIDOa_0olxvEN7BMZgLeRw - - - 101 - - 9756.5420ms. 
2025/08/25 09:04:47.604 ALC:1#PLC点位开始轮询
2025/08/25 09:04:47.606 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:47.637 ALC:后台服务关闭
2025/08/25 09:04:47.638 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:47.641 ALC:获取数据服务停止
2025/08/25 09:04:48.357 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:04:48.358 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 1.2130ms. 
2025/08/25 09:04:48.419 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:04:48.421 CORS policy execution successful. 
2025/08/25 09:04:48.423 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:04:48.427 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:04:48.429 CORS policy execution successful. 
2025/08/25 09:04:48.429 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 1.9837ms. 
2025/08/25 09:04:48.434 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:04:48.434 CORS policy execution successful. 
2025/08/25 09:04:48.435 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:04:48.436 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:04:48.437 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 2.9508ms. 
2025/08/25 09:04:48.452 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:04:48.457 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=K-iDHZ_j6rhbDKSvJburRQ - -. 
2025/08/25 09:04:48.458 CORS policy execution successful. 
2025/08/25 09:04:48.458 Executing endpoint '/socket'. 
2025/08/25 09:04:48.466 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:04:48.467 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 48.0129ms. 
2025/08/25 09:04:55.623 ALC:后台服务启动成功
2025/08/25 09:04:55.634 ALC:自动重启服务成功
2025/08/25 09:04:55.634 ALC:启动自动报警监控
2025/08/25 09:04:55.635 ALC:自动报警监控被关闭
2025/08/25 09:04:55.636 ALC:启动获取数据服务
2025/08/25 09:04:57.632 ALC:1#PLC点位开始轮询
2025/08/25 09:04:57.634 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:04:57.634 ALC:后台服务关闭
2025/08/25 09:04:57.635 ALC:1#PLC点位轮询被关闭
2025/08/25 09:04:57.648 ALC:获取数据服务停止
2025/08/25 09:05:05.638 ALC:后台服务启动成功
2025/08/25 09:05:05.646 ALC:启动自动报警监控
2025/08/25 09:05:05.684 ALC:自动报警监控被关闭
2025/08/25 09:05:05.685 ALC:自动重启服务成功
2025/08/25 09:05:05.686 ALC:启动获取数据服务
2025/08/25 09:05:07.645 ALC:1#PLC点位开始轮询
2025/08/25 09:05:07.646 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:07.646 ALC:后台服务关闭
2025/08/25 09:05:07.647 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:07.695 ALC:获取数据服务停止
2025/08/25 09:05:15.702 ALC:后台服务启动成功
2025/08/25 09:05:15.713 ALC:自动重启服务成功
2025/08/25 09:05:15.714 ALC:启动自动报警监控
2025/08/25 09:05:15.715 ALC:自动报警监控被关闭
2025/08/25 09:05:15.716 ALC:启动获取数据服务
2025/08/25 09:05:17.709 ALC:1#PLC点位开始轮询
2025/08/25 09:05:17.710 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:17.710 ALC:后台服务关闭
2025/08/25 09:05:17.710 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:17.740 ALC:获取数据服务停止
2025/08/25 09:05:25.725 ALC:后台服务启动成功
2025/08/25 09:05:25.736 ALC:自动重启服务成功
2025/08/25 09:05:25.737 ALC:启动自动报警监控
2025/08/25 09:05:25.737 ALC:自动报警监控被关闭
2025/08/25 09:05:25.738 ALC:启动获取数据服务
2025/08/25 09:05:27.742 ALC:1#PLC点位开始轮询
2025/08/25 09:05:27.743 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:27.745 ALC:后台服务关闭
2025/08/25 09:05:27.745 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:27.749 ALC:获取数据服务停止
2025/08/25 09:05:35.748 ALC:后台服务启动成功
2025/08/25 09:05:35.758 ALC:启动自动报警监控
2025/08/25 09:05:35.761 ALC:自动报警监控被关闭
2025/08/25 09:05:35.762 ALC:自动重启服务成功
2025/08/25 09:05:35.763 ALC:启动获取数据服务
2025/08/25 09:05:37.797 ALC:1#PLC点位开始轮询
2025/08/25 09:05:37.799 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:37.800 ALC:后台服务关闭
2025/08/25 09:05:37.800 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:38.802 ALC:获取数据服务停止
2025/08/25 09:05:45.767 ALC:后台服务启动成功
2025/08/25 09:05:45.781 ALC:自动重启服务成功
2025/08/25 09:05:45.782 ALC:启动自动报警监控
2025/08/25 09:05:45.782 ALC:自动报警监控被关闭
2025/08/25 09:05:45.782 ALC:启动获取数据服务
2025/08/25 09:05:47.789 ALC:1#PLC点位开始轮询
2025/08/25 09:05:47.792 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:47.793 ALC:后台服务关闭
2025/08/25 09:05:47.794 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:47.798 ALC:获取数据服务停止
2025/08/25 09:05:55.790 ALC:后台服务启动成功
2025/08/25 09:05:55.798 ALC:启动自动报警监控
2025/08/25 09:05:55.836 ALC:自动报警监控被关闭
2025/08/25 09:05:55.837 ALC:自动重启服务成功
2025/08/25 09:05:55.838 ALC:启动获取数据服务
2025/08/25 09:05:57.801 ALC:1#PLC点位开始轮询
2025/08/25 09:05:57.802 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:05:57.802 ALC:后台服务关闭
2025/08/25 09:05:57.803 ALC:1#PLC点位轮询被关闭
2025/08/25 09:05:57.861 ALC:获取数据服务停止
2025/08/25 09:06:05.854 ALC:后台服务启动成功
2025/08/25 09:06:05.865 ALC:自动重启服务成功
2025/08/25 09:06:05.866 ALC:启动自动报警监控
2025/08/25 09:06:05.867 ALC:自动报警监控被关闭
2025/08/25 09:06:05.867 ALC:启动获取数据服务
2025/08/25 09:06:07.865 ALC:1#PLC点位开始轮询
2025/08/25 09:06:07.866 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:07.867 ALC:后台服务关闭
2025/08/25 09:06:07.867 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:07.876 ALC:获取数据服务停止
2025/08/25 09:06:15.870 ALC:后台服务启动成功
2025/08/25 09:06:15.889 ALC:自动重启服务成功
2025/08/25 09:06:15.890 ALC:启动自动报警监控
2025/08/25 09:06:15.891 ALC:自动报警监控被关闭
2025/08/25 09:06:15.892 ALC:启动获取数据服务
2025/08/25 09:06:17.884 ALC:1#PLC点位开始轮询
2025/08/25 09:06:17.884 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:17.885 ALC:后台服务关闭
2025/08/25 09:06:17.886 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:17.902 ALC:获取数据服务停止
2025/08/25 09:06:25.901 ALC:后台服务启动成功
2025/08/25 09:06:25.911 ALC:启动自动报警监控
2025/08/25 09:06:25.913 ALC:自动报警监控被关闭
2025/08/25 09:06:25.913 ALC:自动重启服务成功
2025/08/25 09:06:25.914 ALC:启动获取数据服务
2025/08/25 09:06:27.910 ALC:1#PLC点位开始轮询
2025/08/25 09:06:27.910 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:27.911 ALC:后台服务关闭
2025/08/25 09:06:27.911 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:27.941 ALC:获取数据服务停止
2025/08/25 09:06:35.920 ALC:后台服务启动成功
2025/08/25 09:06:35.929 ALC:启动自动报警监控
2025/08/25 09:06:35.965 ALC:自动报警监控被关闭
2025/08/25 09:06:35.966 ALC:自动重启服务成功
2025/08/25 09:06:35.966 ALC:启动获取数据服务
2025/08/25 09:06:37.940 ALC:1#PLC点位开始轮询
2025/08/25 09:06:37.941 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:37.941 ALC:后台服务关闭
2025/08/25 09:06:37.942 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:37.974 ALC:获取数据服务停止
2025/08/25 09:06:45.980 ALC:后台服务启动成功
2025/08/25 09:06:45.995 ALC:自动重启服务成功
2025/08/25 09:06:45.996 ALC:启动自动报警监控
2025/08/25 09:06:45.997 ALC:自动报警监控被关闭
2025/08/25 09:06:45.998 ALC:启动获取数据服务
2025/08/25 09:06:47.995 ALC:1#PLC点位开始轮询
2025/08/25 09:06:47.996 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:47.996 ALC:后台服务关闭
2025/08/25 09:06:47.997 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:48.011 ALC:获取数据服务停止
2025/08/25 09:06:56.008 ALC:后台服务启动成功
2025/08/25 09:06:56.025 ALC:自动重启服务成功
2025/08/25 09:06:56.025 ALC:启动自动报警监控
2025/08/25 09:06:56.026 ALC:自动报警监控被关闭
2025/08/25 09:06:56.027 ALC:启动获取数据服务
2025/08/25 09:06:58.019 ALC:1#PLC点位开始轮询
2025/08/25 09:06:58.020 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:06:58.021 ALC:后台服务关闭
2025/08/25 09:06:58.022 ALC:1#PLC点位轮询被关闭
2025/08/25 09:06:58.051 ALC:获取数据服务停止
2025/08/25 09:07:06.037 ALC:后台服务启动成功
2025/08/25 09:07:06.048 ALC:自动重启服务成功
2025/08/25 09:07:06.049 ALC:启动自动报警监控
2025/08/25 09:07:06.050 ALC:自动报警监控被关闭
2025/08/25 09:07:06.051 ALC:启动获取数据服务
2025/08/25 09:07:08.047 ALC:1#PLC点位开始轮询
2025/08/25 09:07:08.048 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:08.049 ALC:后台服务关闭
2025/08/25 09:07:08.050 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:08.057 ALC:获取数据服务停止
2025/08/25 09:07:10.577 Executed endpoint '/socket'. 
2025/08/25 09:07:10.607 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=K-iDHZ_j6rhbDKSvJburRQ - - - 101 - - 142150.0756ms. 
2025/08/25 09:07:11.294 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:07:11.305 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 10.8118ms. 
2025/08/25 09:07:11.387 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:07:11.403 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:07:11.405 CORS policy execution successful. 
2025/08/25 09:07:11.407 CORS policy execution successful. 
2025/08/25 09:07:11.411 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:07:11.408 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 20.3194ms. 
2025/08/25 09:07:11.459 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:07:11.460 CORS policy execution successful. 
2025/08/25 09:07:11.460 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:07:11.461 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:07:11.461 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 39.4868ms. 
2025/08/25 09:07:11.484 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=nwK8WbtJPBQR3i7ddW3z1w - -. 
2025/08/25 09:07:11.485 CORS policy execution successful. 
2025/08/25 09:07:11.484 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:07:11.485 Executing endpoint '/socket'. 
2025/08/25 09:07:11.507 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:07:11.508 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 121.0083ms. 
2025/08/25 09:07:16.156 ALC:后台服务启动成功
2025/08/25 09:07:16.169 ALC:启动自动报警监控
2025/08/25 09:07:16.173 ALC:自动报警监控被关闭
2025/08/25 09:07:16.174 ALC:自动重启服务成功
2025/08/25 09:07:16.175 ALC:启动获取数据服务
2025/08/25 09:07:18.173 ALC:1#PLC点位开始轮询
2025/08/25 09:07:18.174 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:18.174 ALC:后台服务关闭
2025/08/25 09:07:18.174 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:18.204 ALC:获取数据服务停止
2025/08/25 09:07:26.180 ALC:后台服务启动成功
2025/08/25 09:07:26.189 ALC:启动自动报警监控
2025/08/25 09:07:26.191 ALC:自动报警监控被关闭
2025/08/25 09:07:26.192 ALC:自动重启服务成功
2025/08/25 09:07:26.192 ALC:启动获取数据服务
2025/08/25 09:07:28.198 ALC:1#PLC点位开始轮询
2025/08/25 09:07:28.199 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:28.200 ALC:后台服务关闭
2025/08/25 09:07:28.200 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:29.201 ALC:获取数据服务停止
2025/08/25 09:07:36.136 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:07:36.215 ALC:后台服务启动成功
2025/08/25 09:07:36.228 ALC:自动重启服务成功
2025/08/25 09:07:36.229 ALC:启动自动报警监控
2025/08/25 09:07:36.230 ALC:自动报警监控被关闭
2025/08/25 09:07:36.231 ALC:启动获取数据服务
2025/08/25 09:07:38.239 ALC:1#PLC点位开始轮询
2025/08/25 09:07:38.240 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:38.240 ALC:后台服务关闭
2025/08/25 09:07:38.241 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:38.253 ALC:获取数据服务停止
2025/08/25 09:07:46.248 ALC:后台服务启动成功
2025/08/25 09:07:46.312 ALC:启动自动报警监控
2025/08/25 09:07:46.322 ALC:自动重启服务成功
2025/08/25 09:07:46.323 ALC:自动报警监控被关闭
2025/08/25 09:07:46.329 ALC:启动获取数据服务
2025/08/25 09:07:47.660 Request starting HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json -. 
2025/08/25 09:07:47.669 Request finished HTTP/1.1 GET http://127.0.0.1:5009/API/identity/init application/json - - 200 - application/json;+charset=utf-8 12.2019ms. 
2025/08/25 09:07:47.736 Request starting HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2. 
2025/08/25 09:07:47.738 CORS policy execution successful. 
2025/08/25 09:07:47.746 MyMenuListUBAC: 开始获取用户 admin 的菜单权限
2025/08/25 09:07:47.764 Request starting HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - -. 
2025/08/25 09:07:47.817 CORS policy execution successful. 
2025/08/25 09:07:47.822 Request finished HTTP/1.1 OPTIONS http://localhost:5009/socket/negotiate?negotiateVersion=1 - - - 204 - - 59.3829ms. 
2025/08/25 09:07:47.838 Request starting HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0. 
2025/08/25 09:07:47.839 CORS policy execution successful. 
2025/08/25 09:07:47.841 Executing endpoint '/socket/negotiate'. 
2025/08/25 09:07:47.843 Executed endpoint '/socket/negotiate'. 
2025/08/25 09:07:47.844 Request finished HTTP/1.1 POST http://localhost:5009/socket/negotiate?negotiateVersion=1 - 0 - 200 316 application/json 12.0459ms. 
2025/08/25 09:07:47.845 用户 admin 是超级管理员，返回所有菜单
2025/08/25 09:07:47.862 Request starting HTTP/1.1 GET http://localhost:5009/socket?id=6oPfVr5eLZJbxQ2Pz61TGA - -. 
2025/08/25 09:07:47.863 CORS policy execution successful. 
2025/08/25 09:07:47.870 MyMenuListUBAC: 返回菜单数量 16
2025/08/25 09:07:47.870 Executing endpoint '/socket'. 
2025/08/25 09:07:47.871 Request finished HTTP/1.1 POST http://127.0.0.1:5009/API/IFP/Rights/Menu/MyMenuListUBAC application/json 2 - 200 - application/json;+charset=utf-8 134.5047ms. 
2025/08/25 09:07:48.336 ALC:1#PLC点位开始轮询
2025/08/25 09:07:48.337 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:48.337 ALC:后台服务关闭
2025/08/25 09:07:48.338 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:48.345 ALC:获取数据服务停止
2025/08/25 09:07:52.521 [ErrCode:E9999]未知错误|ALC:
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> System.Exception: 维护命令执行失败: PLC写入数据，S7协议报文异常，回复的错误码为0
   at DAL.ALC.Service.ServiceCPU.ExecuteMaintenanceCommand(Dictionary`2 content) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 116
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at DAL.ALC.Service.ServiceCPU.InsertCmd(String CMD, Dictionary`2 param) in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceCPU.cs:line 38
方法名COM.IFP.Log.LoggerHelper.Error，源代码E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.Log\LoggerHelper.cs行78列17，动态库E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Server\bin\Debug\net6.0\COM.IFP.Log.dll。
2025/08/25 09:07:52.539 Executed endpoint '/socket'. 
2025/08/25 09:07:52.540 Request finished HTTP/1.1 GET http://localhost:5009/socket?id=nwK8WbtJPBQR3i7ddW3z1w - - - 101 - - 41055.9177ms. 
2025/08/25 09:07:57.497 ALC:后台服务启动成功
2025/08/25 09:07:57.507 ALC:自动重启服务成功
2025/08/25 09:07:57.507 ALC:启动自动报警监控
2025/08/25 09:07:57.508 ALC:自动报警监控被关闭
2025/08/25 09:07:57.509 ALC:启动获取数据服务
2025/08/25 09:07:59.512 ALC:1#PLC点位开始轮询
2025/08/25 09:07:59.512 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:07:59.513 ALC:后台服务关闭
2025/08/25 09:07:59.514 ALC:1#PLC点位轮询被关闭
2025/08/25 09:07:59.525 ALC:获取数据服务停止
2025/08/25 09:08:07.517 ALC:后台服务启动成功
2025/08/25 09:08:07.526 ALC:自动重启服务成功
2025/08/25 09:08:07.527 ALC:启动自动报警监控
2025/08/25 09:08:07.527 ALC:自动报警监控被关闭
2025/08/25 09:08:07.527 ALC:启动获取数据服务
2025/08/25 09:08:09.527 ALC:1#PLC点位开始轮询
2025/08/25 09:08:09.528 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:09.528 ALC:后台服务关闭
2025/08/25 09:08:09.529 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:09.542 ALC:获取数据服务停止
2025/08/25 09:08:17.535 ALC:后台服务启动成功
2025/08/25 09:08:17.547 ALC:自动重启服务成功
2025/08/25 09:08:17.547 ALC:启动自动报警监控
2025/08/25 09:08:17.548 ALC:自动报警监控被关闭
2025/08/25 09:08:17.549 ALC:启动获取数据服务
2025/08/25 09:08:19.550 ALC:1#PLC点位开始轮询
2025/08/25 09:08:19.553 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:19.554 ALC:后台服务关闭
2025/08/25 09:08:19.555 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:19.564 ALC:获取数据服务停止
2025/08/25 09:08:27.565 ALC:后台服务启动成功
2025/08/25 09:08:27.583 ALC:启动自动报警监控
2025/08/25 09:08:27.587 ALC:自动重启服务成功
2025/08/25 09:08:27.588 ALC:自动报警监控被关闭
2025/08/25 09:08:27.589 ALC:启动获取数据服务
2025/08/25 09:08:29.595 ALC:1#PLC点位开始轮询
2025/08/25 09:08:29.780 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:08:30.061 ALC:后台服务关闭
2025/08/25 09:08:30.407 ALC:1#PLC点位轮询被关闭
2025/08/25 09:08:30.676 ALC:获取数据服务停止
2025/08/25 09:08:42.218 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:08:53.710 ALC:自动重启服务失败。System.IO.IOException: Unable to read data from the transport connection: 远程主机强迫关闭了一个现有的连接。.
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at COM.IFP.PLC.SiemensS7.S7Adaptor.Build() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.IFP.Plugin\COM.IFP.PLC\SiemensS7\S7Adaptor.cs:line 120
   at DAL.ALC.Service.ServiceMain.<AutoRestart>b__8_0() in E:\svn_repo\IFP_Product\5E-RLC_ALC\5E-ALC\5E-ALC9410A\KY.ALC.Plugin\DAL.ALC.Service\ServiceMain.cs:line 194
2025/08/25 09:09:04.041 ALC:后台服务启动成功
2025/08/25 09:09:04.054 ALC:启动自动报警监控
2025/08/25 09:09:04.058 ALC:启动获取数据服务
2025/08/25 09:09:04.059 ALC:自动重启服务成功
2025/08/25 09:09:04.060 ALC:自动报警监控被关闭
2025/08/25 09:09:06.056 ALC:1#PLC点位开始轮询
2025/08/25 09:09:06.056 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:06.057 ALC:后台服务关闭
2025/08/25 09:09:06.058 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:06.087 ALC:获取数据服务停止
2025/08/25 09:09:14.074 ALC:后台服务启动成功
2025/08/25 09:09:14.087 ALC:启动自动报警监控
2025/08/25 09:09:14.087 ALC:自动报警监控被关闭
2025/08/25 09:09:14.088 ALC:启动获取数据服务
2025/08/25 09:09:14.088 ALC:自动重启服务成功
2025/08/25 09:09:16.084 ALC:1#PLC点位开始轮询
2025/08/25 09:09:16.085 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:16.086 ALC:后台服务关闭
2025/08/25 09:09:16.086 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:16.099 ALC:获取数据服务停止
2025/08/25 09:09:24.102 ALC:后台服务启动成功
2025/08/25 09:09:24.112 ALC:启动自动报警监控
2025/08/25 09:09:24.146 ALC:自动报警监控被关闭
2025/08/25 09:09:24.147 ALC:自动重启服务成功
2025/08/25 09:09:24.147 ALC:启动获取数据服务
2025/08/25 09:09:26.115 ALC:1#PLC点位开始轮询
2025/08/25 09:09:26.115 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:26.116 ALC:后台服务关闭
2025/08/25 09:09:26.116 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:26.165 ALC:获取数据服务停止
2025/08/25 09:09:34.163 ALC:后台服务启动成功
2025/08/25 09:09:34.172 ALC:自动重启服务成功
2025/08/25 09:09:34.172 ALC:启动获取数据服务
2025/08/25 09:09:34.172 ALC:启动自动报警监控
2025/08/25 09:09:34.173 ALC:自动报警监控被关闭
2025/08/25 09:09:36.172 ALC:1#PLC点位开始轮询
2025/08/25 09:09:36.173 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:36.174 ALC:后台服务关闭
2025/08/25 09:09:36.174 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:36.187 ALC:获取数据服务停止
2025/08/25 09:09:44.183 ALC:后台服务启动成功
2025/08/25 09:09:44.196 ALC:自动重启服务成功
2025/08/25 09:09:44.197 ALC:启动自动报警监控
2025/08/25 09:09:44.197 ALC:自动报警监控被关闭
2025/08/25 09:09:44.198 ALC:启动获取数据服务
2025/08/25 09:09:46.196 ALC:1#PLC点位开始轮询
2025/08/25 09:09:46.197 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:46.197 ALC:后台服务关闭
2025/08/25 09:09:46.198 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:46.210 ALC:获取数据服务停止
2025/08/25 09:09:54.208 ALC:后台服务启动成功
2025/08/25 09:09:54.220 ALC:自动重启服务成功
2025/08/25 09:09:54.221 ALC:启动自动报警监控
2025/08/25 09:09:54.221 ALC:自动报警监控被关闭
2025/08/25 09:09:54.221 ALC:启动获取数据服务
2025/08/25 09:09:56.219 ALC:1#PLC点位开始轮询
2025/08/25 09:09:56.219 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:09:56.220 ALC:后台服务关闭
2025/08/25 09:09:56.220 ALC:1#PLC点位轮询被关闭
2025/08/25 09:09:56.235 ALC:获取数据服务停止
2025/08/25 09:10:04.228 ALC:后台服务启动成功
2025/08/25 09:10:04.240 ALC:自动重启服务成功
2025/08/25 09:10:04.241 ALC:启动自动报警监控
2025/08/25 09:10:04.241 ALC:自动报警监控被关闭
2025/08/25 09:10:04.241 ALC:启动获取数据服务
2025/08/25 09:10:06.238 ALC:1#PLC点位开始轮询
2025/08/25 09:10:06.239 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:06.239 ALC:后台服务关闭
2025/08/25 09:10:06.240 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:06.271 ALC:获取数据服务停止
2025/08/25 09:10:14.250 ALC:后台服务启动成功
2025/08/25 09:10:14.258 ALC:启动自动报警监控
2025/08/25 09:10:14.293 ALC:自动报警监控被关闭
2025/08/25 09:10:14.294 ALC:自动重启服务成功
2025/08/25 09:10:14.295 ALC:启动获取数据服务
2025/08/25 09:10:16.258 ALC:1#PLC点位开始轮询
2025/08/25 09:10:16.258 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:16.259 ALC:后台服务关闭
2025/08/25 09:10:16.259 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:16.306 ALC:获取数据服务停止
2025/08/25 09:10:24.303 ALC:后台服务启动成功
2025/08/25 09:10:24.312 ALC:启动自动报警监控
2025/08/25 09:10:24.337 ALC:自动报警监控被关闭
2025/08/25 09:10:24.337 ALC:自动重启服务成功
2025/08/25 09:10:24.338 ALC:启动获取数据服务
2025/08/25 09:10:26.321 ALC:1#PLC点位开始轮询
2025/08/25 09:10:26.322 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:26.322 ALC:后台服务关闭
2025/08/25 09:10:26.323 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:26.355 ALC:获取数据服务停止
2025/08/25 09:10:34.349 ALC:后台服务启动成功
2025/08/25 09:10:34.357 ALC:启动自动报警监控
2025/08/25 09:10:34.360 ALC:自动报警监控被关闭
2025/08/25 09:10:34.361 ALC:自动重启服务成功
2025/08/25 09:10:34.361 ALC:启动获取数据服务
2025/08/25 09:10:36.371 ALC:1#PLC点位开始轮询
2025/08/25 09:10:36.372 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:36.372 ALC:后台服务关闭
2025/08/25 09:10:36.373 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:37.376 ALC:获取数据服务停止
2025/08/25 09:10:44.378 ALC:后台服务启动成功
2025/08/25 09:10:44.386 ALC:启动自动报警监控
2025/08/25 09:10:44.388 ALC:自动报警监控被关闭
2025/08/25 09:10:44.389 ALC:自动重启服务成功
2025/08/25 09:10:44.389 ALC:启动获取数据服务
2025/08/25 09:10:46.398 ALC:1#PLC点位开始轮询
2025/08/25 09:10:46.399 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:46.399 ALC:后台服务关闭
2025/08/25 09:10:46.400 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:47.399 ALC:获取数据服务停止
2025/08/25 09:10:54.400 ALC:后台服务启动成功
2025/08/25 09:10:54.411 ALC:自动重启服务成功
2025/08/25 09:10:54.412 ALC:启动自动报警监控
2025/08/25 09:10:54.413 ALC:自动报警监控被关闭
2025/08/25 09:10:54.413 ALC:启动获取数据服务
2025/08/25 09:10:56.411 ALC:1#PLC点位开始轮询
2025/08/25 09:10:56.411 ALC:AssGlobal.RUN_STATUS=True;AssGlobal.PlcIsConnected=False;
2025/08/25 09:10:56.412 ALC:后台服务关闭
2025/08/25 09:10:56.412 ALC:1#PLC点位轮询被关闭
2025/08/25 09:10:56.443 ALC:获取数据服务停止
