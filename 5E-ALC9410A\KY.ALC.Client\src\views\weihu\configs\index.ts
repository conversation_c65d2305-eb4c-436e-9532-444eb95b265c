import type { MaintenanceConfig } from '../types/maintenance'
import { neitongbanlianConfig } from './neitongbanlian'
import { jixiezhuaConfig } from './jixiezhua'
import { chengzhongConfig } from './chengzhong'

/**
 * 维护配置映射表
 */
const configMap: Record<string, MaintenanceConfig> = {
  'neitongbanlian': neitongbanlianConfig,
  'jixiezhua': jixiezhuaConfig,
  'chengzhong': chengzhongConfig,
  // TODO: 添加其他配置
  // 'heyangsuofen': heyangsuofenConfig,
  // 'waitongbanlian': waitongbanlianConfig,
  // 'waitongkaigai': waitongkaigaiConfig
}

/**
 * 获取维护配置
 * @param moduleType 模块类型
 * @returns 维护配置对象
 */
export function getMaintenanceConfig(moduleType: string): MaintenanceConfig | null {
  const config = configMap[moduleType]
  
  if (!config) {
    console.error(`未找到维护配置: ${moduleType}`)
    console.log('可用的配置类型:', Object.keys(configMap))
    return null
  }
  
  console.log(`加载维护配置: ${config.name}`)
  return config
}

/**
 * 获取所有可用的模块类型
 */
export function getAvailableModuleTypes(): string[] {
  return Object.keys(configMap)
}

/**
 * 检查模块类型是否有效
 */
export function isValidModuleType(moduleType: string): boolean {
  return moduleType in configMap
}

/**
 * 获取模块显示名称
 */
export function getModuleDisplayName(moduleType: string): string {
  const config = configMap[moduleType]
  return config ? config.name : moduleType
}
