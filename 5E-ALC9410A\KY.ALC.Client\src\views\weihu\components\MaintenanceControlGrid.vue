<!--
  通用维护控制网格组件
-->
<template>
  <div class="maintenance-control-grid">
    <!-- 渲染模块 -->
    <template v-for="(module, index) in modules" :key="index">
      <!-- 标签页模块 -->
      <div v-if="module.type === 'tabs'" class="tabs-container">
        <!-- 标签页头部 -->
        <div class="tab-header">
          <button
            v-for="tab in module.tabs"
            :key="tab.name"
            :class="['tab-btn', { active: activeTab === tab.name }]"
            @click="activeTab = tab.name"
          >
            {{ tab.label }}
          </button>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-content">
          <div
            v-for="tab in module.tabs"
            :key="tab.name"
            v-show="activeTab === tab.name"
            class="tab-panel"
          >
            <div class="control-grid">
              <MaintenanceControlGroup
                v-for="(controlGroup, groupIndex) in tab.modules"
                :key="groupIndex"
                :config="controlGroup"
                :buttonStates="buttonStates"
                :deviceStatus="deviceStatus"
                :isLoading="isLoading"
                @button-click="$emit('button-click', $event)"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 直接控制组模块 -->
      <div v-else class="direct-control-grid">
        <MaintenanceControlGroup
          :config="module as ControlGroupConfig"
          :buttonStates="buttonStates"
          :deviceStatus="deviceStatus"
          :isLoading="isLoading"
          @button-click="$emit('button-click', $event)"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MaintenanceControlGroup from './MaintenanceControlGroup.vue'
import type { ModuleConfig, ControlGroupConfig, DeviceStates, DeviceStatus } from '../types/maintenance'

interface Props {
  modules: ModuleConfig[]
  buttonStates: DeviceStates
  deviceStatus: DeviceStatus
  isLoading: boolean
}

interface Emits {
  (e: 'button-click', deviceType: string, action: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 当前激活的标签页
const activeTab = ref('motor')

// 初始化默认标签页
const initializeActiveTab = () => {
  const firstTabsModule = props.modules.find(module => module.type === 'tabs')
  if (firstTabsModule && firstTabsModule.tabs && firstTabsModule.tabs.length > 0) {
    activeTab.value = firstTabsModule.tabs[0].name
  }
}

// 组件挂载时初始化
initializeActiveTab()
</script>

<style lang="scss" scoped>
.maintenance-control-grid {
  width: 100%;
  
  .tabs-container {
    .tab-header {
      display: flex;
      gap: 2px;
      margin-bottom: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      padding: 4px;
      
      .tab-btn {
        flex: 1;
        padding: 10px 16px;
        border: none;
        background: transparent;
        color: #6c757d;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #e9ecef;
          color: #495057;
        }
        
        &.active {
          background: #007bff;
          color: white;
          box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }
      }
    }
    
    .tab-content {
      .tab-panel {
        .control-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;
          
          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
  
  .direct-control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}
</style>
