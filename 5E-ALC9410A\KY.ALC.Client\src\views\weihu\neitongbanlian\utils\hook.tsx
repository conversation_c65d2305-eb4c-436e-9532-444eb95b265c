import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useGlobalSignalR } from '@/composables/useSignalR'
import { createControlMessage } from '@/utils/signalr-config'
import { ElMessage } from 'element-plus'

import type {
  ButtonStates,
  DeviceStatus,
  SystemStatus,
  RollerMotorControl,
  ExternalRollerControl,
  CylinderControl,
  UseNeitongbanlianSignalRReturn
} from './types'
import { DeviceType, ActionType } from './types'

/**
 * 内桶板链业务逻辑Hook
 * 按照机械手组件模式重构
 */
export function useNeitongbanlianSignalR(): UseNeitongbanlianSignalRReturn {
  const signalR = useGlobalSignalR()

  // 加载状态
  const isLoading = ref(false)
  
  // 当前活动标签页
  const activeTab = ref('motor')
  
  const commandMap: Record<string, string> = {
    // 滚筒电机控制 - 完整命令映射
    '快检重桶出桶滚筒电机-启动': 'Manu_快检重桶出桶滚筒电机1',
    '快检重桶出桶滚筒电机-停止': 'Manu_快检重桶出桶滚筒电机2',
    '制样重桶出桶滚筒电机-启动': 'Manu_制样重桶出桶滚筒电机1',
    '制样重桶出桶滚筒电机-停止': 'Manu_制样重桶出桶滚筒电机2',
    '存样重桶出桶滚筒电机-启动': 'Manu_存样重桶出桶滚筒电机1',
    '存样重桶出桶滚筒电机-停止': 'Manu_存样重桶出桶滚筒电机2',
    '外部异样桶出桶滚筒电机-正转': 'Manu_外部异样桶出桶滚筒电机正转',
    '外部异样桶出桶滚筒电机-反转': 'Manu_外部异样桶出桶滚筒电机反转',

    // 挡桶气缸控制 - 完整命令映射
    '快检重桶出桶挡桶气缸-伸出': 'Manu_快检重桶出桶挡桶气缸1伸出',
    '快检重桶出桶挡桶气缸-缩回': 'Manu_快检重桶出桶挡桶气缸1缩回',
    '制样重桶出桶挡桶气缸-伸出': 'Manu_制样重桶出桶挡桶气缸1伸出',
    '制样重桶出桶挡桶气缸-缩回': 'Manu_制样重桶出桶挡桶气缸1缩回',
    '存样重桶出桶挡桶气缸-伸出': 'Manu_存样重桶出桶挡桶气缸1伸出',
    '存样重桶出桶挡桶气缸-缩回': 'Manu_存样重桶出桶挡桶气缸1缩回',

    // 弃样重桶出桶挡桶气缸控制
    '弃样重桶出桶挡桶气缸-伸出': 'Manu_弃样重桶出桶挡桶气缸1伸出',
    '弃样重桶出桶挡桶气缸-缩回': 'Manu_弃样重桶出桶挡桶气缸1缩回',

    // 外部异样桶进出桶挡桶气缸控制
    '外部异样桶进出桶挡桶气缸-伸出': 'Manu_外部异样桶进出桶挡桶气缸1伸出',
    '外部异样桶进出桶挡桶气缸-缩回': 'Manu_外部异样桶进出桶挡桶气缸1缩回'
  }
  
  // 响应式状态 - 使用枚举类型
  const buttonStates = reactive<ButtonStates>({
    [DeviceType.QUICK_INSPECT_ROLLER]: { 
      [ActionType.START]: false, 
      [ActionType.STOP]: false 
    },
    [DeviceType.SAMPLE_PREP_ROLLER]: { 
      [ActionType.START]: false, 
      [ActionType.STOP]: false 
    },
    [DeviceType.SAMPLE_STORE_ROLLER]: { 
      [ActionType.START]: false, 
      [ActionType.STOP]: false 
    },
    [DeviceType.EXTERNAL_ROLLER]: { 
      [ActionType.FORWARD]: false, 
      [ActionType.REVERSE]: false 
    },
    [DeviceType.QUICK_INSPECT_CYLINDER]: { 
      [ActionType.EXTEND]: false, 
      [ActionType.RETRACT]: false 
    },
    [DeviceType.SAMPLE_PREP_CYLINDER]: { 
      [ActionType.EXTEND]: false, 
      [ActionType.RETRACT]: false 
    },
    [DeviceType.SAMPLE_STORE_CYLINDER]: { 
      [ActionType.EXTEND]: false, 
      [ActionType.RETRACT]: false 
    }
  })
  
  // 设备状态指示器 - 初始状态为error（红色）
  const deviceStatus = reactive<DeviceStatus>({
    [DeviceType.QUICK_INSPECT_CYLINDER]: {
      '伸出到位': 'error',
      '缩回到位': 'error'
    },
    [DeviceType.SAMPLE_PREP_CYLINDER]: {
      '伸出到位': 'error',
      '缩回到位': 'error'
    },
    [DeviceType.SAMPLE_STORE_CYLINDER]: {
      '伸出到位': 'error',
      '缩回到位': 'error'
    }
  })
  
  // 系统状态
  const systemStatus = reactive<SystemStatus>({
    connected: false
  })

  /**
   * 发送控制命令
   */
  const sendControlCommand = async (deviceType: string, action: string, params: any = {}): Promise<void> => {
    if (isLoading.value || !signalR.isConnected.value) {
      console.warn('SignalR未连接或正在处理其他命令')
      return
    }

    const commandKey = `${deviceType}-${action}`
    const backendCommand = commandMap[commandKey]

    if (!backendCommand) {
      console.error(`未找到命令映射: ${commandKey}`)
      return
    }

    try {
      isLoading.value = true

      // 维护命令统一调用ExecuteMaintenanceCommand，传入PLC常量作为cmd
      const message = createControlMessage('ExecuteMaintenanceCommand', { cmd: backendCommand })

      // 发送命令
      const response = await signalR.send('HandleCommand', JSON.stringify(message))

      // 解析后端返回的JSON响应 - 统一响应解析逻辑
      let result: any = {}
      try {
        if (typeof response === 'string') {
          const parsedResponse = JSON.parse(response)
          if (parsedResponse.C) {
            result = JSON.parse(parsedResponse.C)
          }
        }
      } catch (error) {
        console.error('解析响应失败:', error)
        return
      }

      // 处理响应结果 - 统一结果判断逻辑
      if (result?.Result === 1) {
        ElMessage.success("操作成功")
        console.log(`内桶板链控制命令执行成功: ${deviceType} - ${action}`)
      } else {
        const errorMsg = result?.Msg || '未知错误'
        console.error(`命令执行失败: ${backendCommand}`, errorMsg)
        ElMessage.error(`操作失败: ${errorMsg}`)
      }
    } catch (error) {
      console.error('内桶板链发送控制命令异常:', error)
      ElMessage.error(`命令发送失败: ${error}`)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 切换单个按钮状态
   */
  const toggleSingleButton = async (deviceType: string, action: string): Promise<void> => {
    try {
      // 如果当前按钮已激活，则关闭它
      if (buttonStates[deviceType]?.[action]) {
        buttonStates[deviceType][action] = false
        return
      }
      
      // 否则激活当前按钮，并关闭同设备的其他按钮
      if (buttonStates[deviceType]) {
        Object.keys(buttonStates[deviceType]).forEach(key => {
          buttonStates[deviceType][key] = false
        })
        buttonStates[deviceType][action] = true
        
        // 发送控制命令
        await sendControlCommand(deviceType, action)
      }
    } catch (error) {
      console.error('内桶板链切换按钮状态异常:', error)
      ElMessage.error('按钮状态切换失败')
    }
  }

  /**
   * 滚筒电机控制方法
   */
  const rollerMotorControl: { [key: string]: RollerMotorControl } = {
    [DeviceType.QUICK_INSPECT_ROLLER]: {
      start: (params = {}) => sendControlCommand(DeviceType.QUICK_INSPECT_ROLLER, ActionType.START, params),
      stop: (params = {}) => sendControlCommand(DeviceType.QUICK_INSPECT_ROLLER, ActionType.STOP, params)
    },
    [DeviceType.SAMPLE_PREP_ROLLER]: {
      start: (params = {}) => sendControlCommand(DeviceType.SAMPLE_PREP_ROLLER, ActionType.START, params),
      stop: (params = {}) => sendControlCommand(DeviceType.SAMPLE_PREP_ROLLER, ActionType.STOP, params)
    },
    [DeviceType.SAMPLE_STORE_ROLLER]: {
      start: (params = {}) => sendControlCommand(DeviceType.SAMPLE_STORE_ROLLER, ActionType.START, params),
      stop: (params = {}) => sendControlCommand(DeviceType.SAMPLE_STORE_ROLLER, ActionType.STOP, params)
    }
  }
  
  /**
   * 外部异样桶滚筒电机控制
   */
  const externalRollerControl: ExternalRollerControl = {
    forward: (params = {}) => sendControlCommand(DeviceType.EXTERNAL_ROLLER, ActionType.FORWARD, params),
    reverse: (params = {}) => sendControlCommand(DeviceType.EXTERNAL_ROLLER, ActionType.REVERSE, params)
  }
  
  /**
   * 挡桶气缸控制方法
   */
  const cylinderControl: { [key: string]: CylinderControl } = {
    [DeviceType.QUICK_INSPECT_CYLINDER]: {
      extend: (params = {}) => sendControlCommand(DeviceType.QUICK_INSPECT_CYLINDER, ActionType.EXTEND, params),
      retract: (params = {}) => sendControlCommand(DeviceType.QUICK_INSPECT_CYLINDER, ActionType.RETRACT, params)
    },
    [DeviceType.SAMPLE_PREP_CYLINDER]: {
      extend: (params = {}) => sendControlCommand(DeviceType.SAMPLE_PREP_CYLINDER, ActionType.EXTEND, params),
      retract: (params = {}) => sendControlCommand(DeviceType.SAMPLE_PREP_CYLINDER, ActionType.RETRACT, params)
    },
    [DeviceType.SAMPLE_STORE_CYLINDER]: {
      extend: (params = {}) => sendControlCommand(DeviceType.SAMPLE_STORE_CYLINDER, ActionType.EXTEND, params),
      retract: (params = {}) => sendControlCommand(DeviceType.SAMPLE_STORE_CYLINDER, ActionType.RETRACT, params)
    }
  }

  /**
   * 处理实时数据更新
   */
  const handleRealtimeData = (data: any) => {
    try {
      // 解析SignalR消息
      let parsedData = data
      if (typeof data === 'string') {
        parsedData = JSON.parse(data)
      }

      // 处理SignalR消息格式
      if (parsedData && parsedData.type && parsedData.arguments) {
        if (parsedData.arguments.length > 0) {
          const messageContent = parsedData.arguments[0]
          parsedData = typeof messageContent === 'string' ? JSON.parse(messageContent) : messageContent
        }
      }

      // 处理C字段格式
      if (parsedData && parsedData.C) {
        parsedData = typeof parsedData.C === 'string' ? JSON.parse(parsedData.C) : parsedData.C
      }

      if (!parsedData || typeof parsedData !== 'object') return

      // 处理数据更新
      Object.entries(parsedData).forEach(([key, value]) => {
        const boolValue = Boolean(value)

        // 清理键名
        let cleanKey = key.replace(/\\u0022/g, '"').replace(/^"/, '').replace(/"$/, '')
        if (cleanKey.startsWith('S_')) cleanKey = cleanKey.substring(2)

        // 滚筒电机状态更新
        if (cleanKey.includes('快检重桶出桶滚筒电机1') || key.includes('Manu_快检重桶出桶滚筒电机1')) {
          buttonStates[DeviceType.QUICK_INSPECT_ROLLER][ActionType.START] = boolValue
          if (boolValue) buttonStates[DeviceType.QUICK_INSPECT_ROLLER][ActionType.STOP] = false
        }
        else if (cleanKey.includes('快检重桶出桶滚筒电机2') || key.includes('Manu_快检重桶出桶滚筒电机2')) {
          buttonStates[DeviceType.QUICK_INSPECT_ROLLER][ActionType.STOP] = boolValue
          if (boolValue) buttonStates[DeviceType.QUICK_INSPECT_ROLLER][ActionType.START] = false
        }
        else if (cleanKey.includes('制样重桶出桶滚筒电机1') || key.includes('Manu_制样重桶出桶滚筒电机1')) {
          buttonStates[DeviceType.SAMPLE_PREP_ROLLER][ActionType.START] = boolValue
          if (boolValue) buttonStates[DeviceType.SAMPLE_PREP_ROLLER][ActionType.STOP] = false
        }
        else if (cleanKey.includes('制样重桶出桶滚筒电机2') || key.includes('Manu_制样重桶出桶滚筒电机2')) {
          buttonStates[DeviceType.SAMPLE_PREP_ROLLER][ActionType.STOP] = boolValue
          if (boolValue) buttonStates[DeviceType.SAMPLE_PREP_ROLLER][ActionType.START] = false
        }
        else if (cleanKey.includes('存样重桶出桶滚筒电机1') || key.includes('Manu_存样重桶出桶滚筒电机1')) {
          buttonStates[DeviceType.SAMPLE_STORE_ROLLER][ActionType.START] = boolValue
          if (boolValue) buttonStates[DeviceType.SAMPLE_STORE_ROLLER][ActionType.STOP] = false
        }
        else if (cleanKey.includes('存样重桶出桶滚筒电机2') || key.includes('Manu_存样重桶出桶滚筒电机2')) {
          buttonStates[DeviceType.SAMPLE_STORE_ROLLER][ActionType.STOP] = boolValue
          if (boolValue) buttonStates[DeviceType.SAMPLE_STORE_ROLLER][ActionType.START] = false
        }
        else if (cleanKey.includes('外部异样桶出桶滚筒电机正转') || key.includes('Manu_外部异样桶出桶滚筒电机正转')) {
          buttonStates[DeviceType.EXTERNAL_ROLLER][ActionType.FORWARD] = boolValue
          if (boolValue) buttonStates[DeviceType.EXTERNAL_ROLLER][ActionType.REVERSE] = false
        }
        else if (cleanKey.includes('外部异样桶出桶滚筒电机反转') || key.includes('Manu_外部异样桶出桶滚筒电机反转')) {
          buttonStates[DeviceType.EXTERNAL_ROLLER][ActionType.REVERSE] = boolValue
          if (boolValue) buttonStates[DeviceType.EXTERNAL_ROLLER][ActionType.FORWARD] = false
        }

        // 气缸状态更新
        else if (cleanKey.includes('快检重桶出桶挡桶气缸1伸出') || key.includes('Manu_快检重桶出桶挡桶气缸1伸出')) {
          buttonStates[DeviceType.QUICK_INSPECT_CYLINDER][ActionType.EXTEND] = boolValue
          if (boolValue) buttonStates[DeviceType.QUICK_INSPECT_CYLINDER][ActionType.RETRACT] = false
        }
        else if (cleanKey.includes('快检重桶出桶挡桶气缸1缩回') || key.includes('Manu_快检重桶出桶挡桶气缸1缩回')) {
          buttonStates[DeviceType.QUICK_INSPECT_CYLINDER][ActionType.RETRACT] = boolValue
          if (boolValue) buttonStates[DeviceType.QUICK_INSPECT_CYLINDER][ActionType.EXTEND] = false
        }
        // 其他气缸状态更新逻辑...
      })
    } catch (error) {
      console.error('内桶板链处理实时数据异常:', error)
    }
  }

  /**
   * 获取系统状态
   */
  const getSystemStatus = (): SystemStatus => {
    return systemStatus
  }

  /**
   * 检查连接状态
   */
  const checkConnection = (): boolean => {
    return signalR.isConnected.value
  }

  // 组件挂载时设置监听
  onMounted(() => {
    // 注册监听器
    const registerListener = () => {
      signalR.on('Message', handleRealtimeData)
    }

    // 如果已经连接，直接注册监听器
    if (signalR.isConnected.value) {
      systemStatus.connected = true
      registerListener()
    } else {
      // 定期检查连接状态
      const connectionCheckInterval = setInterval(() => {
        if (signalR.isConnected.value) {
          systemStatus.connected = true
          registerListener()
          clearInterval(connectionCheckInterval)
        }
      }, 1000)

      // 超时后停止检查
      setTimeout(() => {
        clearInterval(connectionCheckInterval)
      }, 5000)
    }
  })

  // 组件卸载时清理监听
  onUnmounted(() => {
    signalR.off('Message', handleRealtimeData)
  })

  return {
    // 状态
    buttonStates,
    deviceStatus,
    systemStatus,
    isLoading,
    activeTab,

    // 控制方法
    rollerMotorControl,
    externalRollerControl,
    cylinderControl,
    toggleSingleButton,

    // 工具方法
    sendControlCommand,
    getSystemStatus,
    checkConnection
  }
}
