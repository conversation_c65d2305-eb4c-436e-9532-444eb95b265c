/**
 * 动态路由适配器
 * 为不同的动态路由框架提供适配接口
 */

import { getAvailableModuleTypes, getModuleDisplayName, getMaintenanceConfig } from '../configs'

/**
 * 通用动态路由信息接口
 */
export interface DynamicRouteInfo {
  path: string
  name: string
  component: string
  meta: {
    title: string
    module: string
    category: string
    requiresAuth: boolean
    icon?: string
    order?: number
    description?: string
    [key: string]: any
  }
}

/**
 * 菜单项接口
 */
export interface MenuItemInfo {
  id: string
  title: string
  path: string
  icon?: string
  order?: number
  category?: string
  children?: MenuItemInfo[]
}

/**
 * 动态路由适配器类
 */
export class DynamicRouteAdapter {
  /**
   * 获取所有维护模块的路由信息
   */
  static getRouteInfoList(): DynamicRouteInfo[] {
    const availableModules = getAvailableModuleTypes()
    
    return availableModules.map(moduleType => {
      const config = getMaintenanceConfig(moduleType)
      
      return {
        path: `/weihu-new/${moduleType}`,
        name: `UnifiedMaintenance_${moduleType}`,
        component: 'MaintenancePanel',
        meta: {
          title: config?.name || getModuleDisplayName(moduleType),
          module: moduleType,
          category: 'maintenance',
          requiresAuth: true,
          icon: this.getModuleIcon(moduleType),
          order: this.getModuleOrder(moduleType),
          description: `${config?.name || moduleType}维护管理`
        }
      }
    })
  }

  /**
   * 获取菜单项信息
   */
  static getMenuItems(): MenuItemInfo[] {
    const routeInfoList = this.getRouteInfoList()
    
    return routeInfoList.map(route => ({
      id: route.name,
      title: route.meta.title,
      path: route.path,
      icon: route.meta.icon,
      order: route.meta.order,
      category: route.meta.category
    }))
  }

  /**
   * 获取分组菜单项
   */
  static getGroupedMenuItems(): MenuItemInfo[] {
    const menuItems = this.getMenuItems()
    
    // 按类别分组
    const grouped: MenuItemInfo[] = [
      {
        id: 'maintenance-group',
        title: '维护管理',
        path: '',
        icon: 'maintenance',
        order: 100,
        category: 'group',
        children: menuItems.sort((a, b) => (a.order || 999) - (b.order || 999))
      }
    ]
    
    return grouped
  }

  /**
   * 为特定框架生成路由配置
   */
  static generateForFramework(framework: 'vue-router' | 'custom' = 'vue-router') {
    const routeInfoList = this.getRouteInfoList()
    
    switch (framework) {
      case 'vue-router':
        return this.generateVueRouterConfig(routeInfoList)
      case 'custom':
        return this.generateCustomConfig(routeInfoList)
      default:
        return routeInfoList
    }
  }

  /**
   * 生成Vue Router配置
   */
  private static generateVueRouterConfig(routeInfoList: DynamicRouteInfo[]) {
    return routeInfoList.map(route => ({
      path: route.path,
      name: route.name,
      component: () => import('../MaintenancePanel.vue'),
      props: { module: route.meta.module },
      meta: route.meta
    }))
  }

  /**
   * 生成自定义框架配置
   */
  private static generateCustomConfig(routeInfoList: DynamicRouteInfo[]) {
    return {
      routes: routeInfoList,
      component: 'MaintenancePanel',
      basePath: '/weihu-new',
      category: 'maintenance'
    }
  }

  /**
   * 获取模块图标
   */
  private static getModuleIcon(moduleType: string): string {
    const iconMap: Record<string, string> = {
      'neitongbanlian': 'conveyor-belt',
      'jixiezhua': 'robot-arm',
      'chengzhong': 'scale',
      'heyangsuofen': 'mixer',
      'waitongbanlian': 'conveyor',
      'waitongkaigai': 'lid-opener'
    }
    return iconMap[moduleType] || 'maintenance'
  }

  /**
   * 获取模块排序权重
   */
  private static getModuleOrder(moduleType: string): number {
    const orderMap: Record<string, number> = {
      'neitongbanlian': 1,
      'jixiezhua': 2,
      'chengzhong': 3,
      'heyangsuofen': 4,
      'waitongbanlian': 5,
      'waitongkaigai': 6
    }
    return orderMap[moduleType] || 999
  }

  /**
   * 验证模块是否存在
   */
  static validateModule(moduleType: string): boolean {
    const availableModules = getAvailableModuleTypes()
    return availableModules.includes(moduleType)
  }

  /**
   * 获取模块配置信息（用于动态路由验证）
   */
  static getModuleInfo(moduleType: string) {
    if (!this.validateModule(moduleType)) {
      return null
    }

    const config = getMaintenanceConfig(moduleType)
    return {
      moduleType,
      name: config?.name || moduleType,
      title: config?.title || `${moduleType}设备图`,
      imagePath: config?.imagePath,
      available: true
    }
  }
}

/**
 * 便捷导出函数
 */
export const getMaintenanceRoutes = () => DynamicRouteAdapter.getRouteInfoList()
export const getMaintenanceMenus = () => DynamicRouteAdapter.getMenuItems()
export const getGroupedMaintenanceMenus = () => DynamicRouteAdapter.getGroupedMenuItems()
export const validateMaintenanceModule = (moduleType: string) => DynamicRouteAdapter.validateModule(moduleType)
export const getMaintenanceModuleInfo = (moduleType: string) => DynamicRouteAdapter.getModuleInfo(moduleType)

/**
 * 动态路由注册助手
 */
export function registerMaintenanceWithDynamicRouter(dynamicRouter: any) {
  const routes = getMaintenanceRoutes()
  
  // 假设动态路由框架有 addRoutes 方法
  if (typeof dynamicRouter.addRoutes === 'function') {
    dynamicRouter.addRoutes(routes)
  }
  // 或者有 registerRoute 方法
  else if (typeof dynamicRouter.registerRoute === 'function') {
    routes.forEach(route => {
      dynamicRouter.registerRoute(route)
    })
  }
  // 或者其他注册方式
  else {
    console.warn('未知的动态路由框架，请手动注册路由')
    console.log('路由信息:', routes)
  }
  
  return routes
}
