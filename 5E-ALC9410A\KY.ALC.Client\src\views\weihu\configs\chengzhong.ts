import type { MaintenanceConfig } from '../types/maintenance'

/**
 * 称重（内桶开盖）维护配置
 */
export const chengzhongConfig: MaintenanceConfig = {
  name: '称重开盖',
  title: '称重开盖设备图',
  imagePath: '/src/assets/images/neitongkaigai.png',

  modules: [
    {
      type: 'control-group',
      title: '内桶读写卡',
      deviceType: 'INNER_BUCKET_CARD',
      buttons: [
        { action: 'READ_WRITE', label: '读写卡', style: 'primary' }
      ]
    },
    {
      type: 'control-group',
      title: '称重清零',
      deviceType: 'WEIGHT_ZERO',
      buttons: [
        { action: 'ZERO', label: '称重清零', style: 'success' }
      ]
    },
    {
      type: 'control-group',
      title: '密码盖遥控',
      deviceType: 'PASSWORD_COVER_REMOTE',
      buttons: [
        { action: 'UNLOCK', label: '开锁', style: 'success' },
        { action: 'LOCK', label: '闭锁', style: 'danger' }
      ]
    },
    {
      type: 'control-group',
      title: '密码盖供电',
      deviceType: 'PASSWORD_COVER_POWER',
      buttons: [
        { action: 'POWER', label: '供电', style: 'warning' }
      ]
    },
    {
      type: 'control-group-with-status',
      title: '夹盖气缸',
      deviceType: 'CLAMP_COVER_CYLINDER',
      buttons: [
        { action: 'EXTEND', label: '伸出', style: 'primary' },
        { action: 'RETRACT', label: '缩回', style: 'info' }
      ],
      statusIndicators: [
        { key: '伸出到位', label: '伸出到位' },
        { key: '缩回到位', label: '缩回到位' }
      ]
    },
    {
      type: 'control-group-with-status',
      title: '压盖气缸',
      deviceType: 'PRESS_COVER_CYLINDER',
      buttons: [
        { action: 'EXTEND', label: '伸出', style: 'primary' },
        { action: 'RETRACT', label: '缩回', style: 'info' }
      ],
      statusIndicators: [
        { key: '伸出到位', label: '伸出到位' },
        { key: '缩回到位', label: '缩回到位' }
      ]
    },
    {
      type: 'control-group',
      title: '开盖步控制',
      deviceType: 'OPEN_COVER_STEP',
      buttons: [
        { action: 'INIT', label: '初始化', style: 'info' },
        { action: 'TEST', label: '测试', style: 'warning' }
      ]
    },
    {
      type: 'control-group',
      title: '锁盖步控制',
      deviceType: 'LOCK_COVER_STEP',
      buttons: [
        { action: 'INIT', label: '初始化', style: 'info' },
        { action: 'TEST', label: '测试', style: 'warning' }
      ]
    }
  ],

  // 命令映射
  commandMap: {
    // 读写卡功能
    'INNER_BUCKET_CARD-READ_WRITE': 'Manu_内桶开盖工位读写卡',
    
    // 称重功能
    'WEIGHT_ZERO-ZERO': 'Manu_內桶开盖工位称重清零',
    
    // 密码盖控制
    'PASSWORD_COVER_REMOTE-UNLOCK': 'Manu_內桶开盖工位密码盖遥控开锁',
    'PASSWORD_COVER_REMOTE-LOCK': 'Manu_內桶开盖工位密码盖遥控闭锁',
    'PASSWORD_COVER_POWER-POWER': 'Manu_內桶开盖工位密码盖供电',
    
    // 夹盖气缸控制
    'CLAMP_COVER_CYLINDER-EXTEND': 'Manu_内桶开盖工位夹盖气缸伸出',
    'CLAMP_COVER_CYLINDER-RETRACT': 'Manu_内桶开盖工位夹盖气缸缩回',
    
    // 压盖气缸控制
    'PRESS_COVER_CYLINDER-EXTEND': 'Manu_内桶开盖工位压盖气缸伸出',
    'PRESS_COVER_CYLINDER-RETRACT': 'Manu_内桶开盖工位压盖气缸缩回',
    
    // 开盖步骤控制
    'OPEN_COVER_STEP-INIT': 'Manu_內桶开盖工位开盖步初始化',
    'OPEN_COVER_STEP-TEST': 'Manu_內桶开盖工位开盖步测试',
    'LOCK_COVER_STEP-INIT': 'Manu_內桶开盖工位锁盖步初始化',
    'LOCK_COVER_STEP-TEST': 'Manu_內桶开盖工位锁盖步测试'
  },

  // 实时数据处理器
  realtimeHandlers: {
    // 读写卡状态处理
    'Manu_内桶开盖工位读写卡': (value, buttonStates) => {
      buttonStates.INNER_BUCKET_CARD.READ_WRITE = value
    },
    
    // 称重清零状态处理
    'Manu_內桶开盖工位称重清零': (value, buttonStates) => {
      buttonStates.WEIGHT_ZERO.ZERO = value
    },
    
    // 密码盖控制状态处理
    'Manu_內桶开盖工位密码盖遥控开锁': (value, buttonStates) => {
      buttonStates.PASSWORD_COVER_REMOTE.UNLOCK = value
      if (value) buttonStates.PASSWORD_COVER_REMOTE.LOCK = false
    },
    'Manu_內桶开盖工位密码盖遥控闭锁': (value, buttonStates) => {
      buttonStates.PASSWORD_COVER_REMOTE.LOCK = value
      if (value) buttonStates.PASSWORD_COVER_REMOTE.UNLOCK = false
    },
    'Manu_內桶开盖工位密码盖供电': (value, buttonStates) => {
      buttonStates.PASSWORD_COVER_POWER.POWER = value
    },
    
    // 夹盖气缸状态处理
    'Manu_内桶开盖工位夹盖气缸伸出': (value, buttonStates) => {
      buttonStates.CLAMP_COVER_CYLINDER.EXTEND = value
      if (value) buttonStates.CLAMP_COVER_CYLINDER.RETRACT = false
    },
    'Manu_内桶开盖工位夹盖气缸缩回': (value, buttonStates) => {
      buttonStates.CLAMP_COVER_CYLINDER.RETRACT = value
      if (value) buttonStates.CLAMP_COVER_CYLINDER.EXTEND = false
    },
    
    // 压盖气缸状态处理
    'Manu_内桶开盖工位压盖气缸伸出': (value, buttonStates) => {
      buttonStates.PRESS_COVER_CYLINDER.EXTEND = value
      if (value) buttonStates.PRESS_COVER_CYLINDER.RETRACT = false
    },
    'Manu_内桶开盖工位压盖气缸缩回': (value, buttonStates) => {
      buttonStates.PRESS_COVER_CYLINDER.RETRACT = value
      if (value) buttonStates.PRESS_COVER_CYLINDER.EXTEND = false
    },
    
    // 开盖步骤状态处理
    'Manu_內桶开盖工位开盖步初始化': (value, buttonStates) => {
      buttonStates.OPEN_COVER_STEP.INIT = value
    },
    'Manu_內桶开盖工位开盖步测试': (value, buttonStates) => {
      buttonStates.OPEN_COVER_STEP.TEST = value
    },
    'Manu_內桶开盖工位锁盖步初始化': (value, buttonStates) => {
      buttonStates.LOCK_COVER_STEP.INIT = value
    },
    'Manu_內桶开盖工位锁盖步测试': (value, buttonStates) => {
      buttonStates.LOCK_COVER_STEP.TEST = value
    },
    
    // 气缸到位状态处理
    'S_内桶开盖工位夹盖气缸伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.CLAMP_COVER_CYLINDER) {
        deviceStatus.CLAMP_COVER_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_内桶开盖工位夹盖气缸缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.CLAMP_COVER_CYLINDER) {
        deviceStatus.CLAMP_COVER_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    },
    'S_内桶开盖工位压盖气缸伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.PRESS_COVER_CYLINDER) {
        deviceStatus.PRESS_COVER_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_内桶开盖工位压盖气缸缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.PRESS_COVER_CYLINDER) {
        deviceStatus.PRESS_COVER_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    }
  }
}
