import type { MaintenanceConfig } from '../types/maintenance'

/**
 * 机械爪维护配置
 */
export const jixiezhuaConfig: MaintenanceConfig = {
  name: '机械爪',
  title: '机械夹爪设备图',
  imagePath: '/src/assets/images/jixieshou.png',

  modules: [
    {
      type: 'control-group',
      title: '机器人控制',
      deviceType: 'ROBOT',
      buttons: [
        { action: 'START', label: '启动', style: 'primary' },
        { action: 'STOP', label: '停止', style: 'danger' },
        { action: 'RESET', label: '复位', style: 'warning' }
      ]
    },
    {
      type: 'control-group',
      title: '机器人设置速度',
      deviceType: 'ROBOT_SPEED',
      buttons: [
        { action: 'SET', label: '设置', style: 'info' }
      ]
    },
    {
      type: 'control-group',
      title: '机器人程序初始化',
      deviceType: 'ROBOT_INIT',
      buttons: [
        { action: 'INIT', label: '初始化', style: 'success' }
      ]
    },
    {
      type: 'control-group',
      title: '机器人状态机重置',
      deviceType: 'ROBOT_STATE_RESET',
      buttons: [
        { action: 'RESET', label: '重置', style: 'warning' }
      ]
    },
    {
      type: 'control-group',
      title: '机器人任务读取',
      deviceType: 'ROBOT_TASK_READ',
      buttons: [
        { action: 'READ', label: '读取', style: 'info' }
      ]
    },
    {
      type: 'control-group-with-status',
      title: '夹爪气缸',
      deviceType: 'GRIPPER_CYLINDER',
      buttons: [
        { action: 'EXTEND', label: '伸出', style: 'primary' },
        { action: 'RETRACT', label: '缩回', style: 'info' }
      ],
      statusIndicators: [
        { key: '伸出到位', label: '伸出到位' },
        { key: '缩回到位', label: '缩回到位' }
      ]
    }
  ],

  // 命令映射
  commandMap: {
    // 机器人控制
    'ROBOT-START': 'Manu_机器人启动',
    'ROBOT-STOP': 'Manu_机器人停止',
    'ROBOT-RESET': 'Manu_机器人复位',
    
    // 机器人功能
    'ROBOT_SPEED-SET': 'Manu_机器人设置速度',
    'ROBOT_INIT-INIT': 'Manu_机器人程序初始化',
    'ROBOT_STATE_RESET-RESET': 'Manu_机器人状态机重置',
    'ROBOT_TASK_READ-READ': 'Manu_机器人任务读取',
    
    // 夹爪控制
    'GRIPPER_CYLINDER-EXTEND': 'Manu_夹爪气缸伸出',
    'GRIPPER_CYLINDER-RETRACT': 'Manu_夹爪气缸缩回'
  },

  // 实时数据处理器
  realtimeHandlers: {
    // 机器人状态处理
    'Manu_机器人启动': (value, buttonStates) => {
      buttonStates.ROBOT.START = value
      if (value) buttonStates.ROBOT.STOP = false
    },
    'Manu_机器人停止': (value, buttonStates) => {
      buttonStates.ROBOT.STOP = value
      if (value) buttonStates.ROBOT.START = false
    },
    'Manu_机器人复位': (value, buttonStates) => {
      buttonStates.ROBOT.RESET = value
    },
    
    // 机器人功能状态处理
    'Manu_机器人设置速度': (value, buttonStates) => {
      buttonStates.ROBOT_SPEED.SET = value
    },
    'Manu_机器人程序初始化': (value, buttonStates) => {
      buttonStates.ROBOT_INIT.INIT = value
    },
    'Manu_机器人状态机重置': (value, buttonStates) => {
      buttonStates.ROBOT_STATE_RESET.RESET = value
    },
    'Manu_机器人任务读取': (value, buttonStates) => {
      buttonStates.ROBOT_TASK_READ.READ = value
    },
    
    // 夹爪气缸状态处理
    'Manu_夹爪气缸伸出': (value, buttonStates) => {
      buttonStates.GRIPPER_CYLINDER.EXTEND = value
      if (value) buttonStates.GRIPPER_CYLINDER.RETRACT = false
    },
    'Manu_夹爪气缸缩回': (value, buttonStates) => {
      buttonStates.GRIPPER_CYLINDER.RETRACT = value
      if (value) buttonStates.GRIPPER_CYLINDER.EXTEND = false
    },
    
    // 夹爪气缸到位状态处理
    'S_夹爪气缸伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.GRIPPER_CYLINDER) {
        deviceStatus.GRIPPER_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_夹爪气缸缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.GRIPPER_CYLINDER) {
        deviceStatus.GRIPPER_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    }
  }
}
