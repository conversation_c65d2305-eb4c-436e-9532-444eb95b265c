/**
 * 统一维护组件动态路由配置
 *
 * 支持动态路由框架，自动根据配置生成路由信息
 */

import type { RouteRecordRaw } from 'vue-router'
import { getAvailableModuleTypes, getModuleDisplayName } from './configs'

/**
 * 动态路由配置生成器
 * 根据维护配置自动生成路由信息
 */
export function generateMaintenanceRoutes(): RouteRecordRaw[] {
  const availableModules = getAvailableModuleTypes()

  return [
    // 通用动态路由
    {
      path: '/weihu-new/:module',
      name: 'UnifiedMaintenance',
      component: () => import('./MaintenancePanel.vue'),
      props: true,
      meta: {
        title: '维护管理',
        requiresAuth: true,
        // 动态路由验证函数
        beforeEnter: (to: any) => {
          const module = to.params.module
          if (!availableModules.includes(module)) {
            console.error(`无效的维护模块: ${module}`)
            console.log('可用模块:', availableModules)
            return false
          }
          return true
        }
      }
    }
  ]
}

/**
 * 为动态路由框架提供的路由信息
 * 返回所有可用的维护模块路由信息
 */
export function getMaintenanceRouteInfo() {
  const availableModules = getAvailableModuleTypes()

  return availableModules.map(moduleType => ({
    path: `/weihu-new/${moduleType}`,
    name: `Maintenance_${moduleType}`,
    component: 'MaintenancePanel', // 组件名称，由动态路由框架处理
    meta: {
      title: getModuleDisplayName(moduleType),
      module: moduleType,
      category: 'maintenance',
      requiresAuth: true,
      icon: 'maintenance', // 可以根据模块类型设置不同图标
      order: getModuleOrder(moduleType)
    }
  }))
}

/**
 * 获取模块排序权重
 */
function getModuleOrder(moduleType: string): number {
  const orderMap: Record<string, number> = {
    'neitongbanlian': 1,
    'jixiezhua': 2,
    'chengzhong': 3,
    'heyangsuofen': 4,
    'waitongbanlian': 5,
    'waitongkaigai': 6
  }
  return orderMap[moduleType] || 999
}

/**
 * 动态路由注册函数
 * 供动态路由框架调用
 */
export function registerMaintenanceRoutes(router: any) {
  const routes = generateMaintenanceRoutes()

  routes.forEach(route => {
    router.addRoute(route)
  })

  console.log('统一维护组件路由已注册:', routes.length, '个路由')
  return routes
}

/**
 * 检查路由是否为维护模块路由
 */
export function isMaintenanceRoute(path: string): boolean {
  return path.startsWith('/weihu-new/') || path.startsWith('/weihu/')
}

/**
 * 从路径提取模块类型
 */
export function extractModuleFromPath(path: string): string | null {
  const match = path.match(/\/weihu(?:-new)?\/([^\/]+)/)
  return match ? match[1] : null
}

// 兼容性导出（保持向后兼容）
export const dynamicMaintenanceRoutes = generateMaintenanceRoutes()
export const maintenanceRouteInfo = getMaintenanceRouteInfo()
