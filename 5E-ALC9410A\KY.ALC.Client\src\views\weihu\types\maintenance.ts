/**
 * 统一维护组件系统类型定义
 */

// 基础设备状态类型
export interface DeviceStates {
  [deviceType: string]: {
    [action: string]: boolean
  }
}

// 设备状态指示器类型
export interface DeviceStatus {
  [deviceType: string]: {
    [statusName: string]: 'success' | 'error' | 'warning'
  }
}

// 按钮配置
export interface ButtonConfig {
  action: string
  label: string
  style?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  disabled?: boolean
}

// 状态指示器配置
export interface StatusIndicatorConfig {
  key: string
  label: string
}

// 控制组配置
export interface ControlGroupConfig {
  type: 'control-group' | 'control-group-with-status'
  title: string
  deviceType: string
  buttons: ButtonConfig[]
  statusIndicators?: StatusIndicatorConfig[]
}

// 标签页配置
export interface TabConfig {
  name: string
  label: string
  modules: ControlGroupConfig[]
}

// 模块配置（支持标签页或直接控制组）
export interface ModuleConfig {
  type: 'tabs' | 'control-group' | 'control-group-with-status'
  tabs?: TabConfig[]
  // 如果是直接控制组，继承ControlGroupConfig的属性
  title?: string
  deviceType?: string
  buttons?: ButtonConfig[]
  statusIndicators?: StatusIndicatorConfig[]
}

// 实时数据处理器类型
export type RealtimeHandler = (
  value: boolean, 
  buttonStates: DeviceStates, 
  deviceStatus: DeviceStatus
) => void

// 主配置接口
export interface MaintenanceConfig {
  name: string
  title: string
  imagePath: string
  modules: ModuleConfig[]
  commandMap: Record<string, string>
  realtimeHandlers: Record<string, RealtimeHandler>
}

// Hook返回类型
export interface UseMaintenanceReturn {
  buttonStates: DeviceStates
  deviceStatus: DeviceStatus
  isLoading: Ref<boolean>
  activeTab: Ref<string>
  handleButtonClick: (deviceType: string, action: string) => Promise<void>
}

// 配置加载器类型
export type ConfigLoader = (moduleType: string) => MaintenanceConfig | null

// SignalR消息解析结果
export interface ParsedSignalRData {
  [key: string]: any
}

// 命令执行结果
export interface CommandResult {
  success: boolean
  message?: string
  data?: any
}
