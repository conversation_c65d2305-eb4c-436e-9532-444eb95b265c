{"format": 1, "restore": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\API.ALC.SignalR\\API.ALC.SignalR.csproj": {}}, "projects": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\API.ALC.SignalR\\API.ALC.SignalR.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\API.ALC.SignalR\\API.ALC.SignalR.csproj", "projectName": "API.ALC.SignalR", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\API.ALC.SignalR\\API.ALC.SignalR.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\API.ALC.SignalR\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\DAL.ALC.Service.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\DAL.ALC.Service.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\KY.IFP.ServiceV2.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\KY.IFP.ServiceV2.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.SignalR.Core": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\DAL.ALC.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\DAL.ALC.Service.csproj", "projectName": "DAL.ALC.Service", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\DAL.ALC.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\DAL.ALC.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\DAL.ALC.www.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\DAL.ALC.www.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\DAL.ALC.www.csproj", "projectName": "DAL.ALC.www", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\DAL.ALC.www.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\DAL.ALC.www\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj", "projectName": "ORM.ALC.www", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\ORM.ALC.www.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.ALC.Plugin\\ORM.ALC.www\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj", "projectName": "API.IFP.SignalR", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj", "projectName": "COM.IFP.Client", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj", "projectName": "COM.IFP.Common", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj", "projectName": "COM.IFP.ComputerInfo", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj", "projectName": "COM.IFP.Device", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj", "projectName": "COM.IFP.Dictionary", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj", "projectName": "COM.IFP.Log", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj", "projectName": "COM.IFP.PLC", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.IO.Ports": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj", "projectName": "COM.IFP.SqlSugarN", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\SqlSugar.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\SqlSugar.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj", "projectName": "COM.IFP.UDPHelper", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj", "projectName": "DAL.IFP.Cookie", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.<PERSON>ie\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\DAL.IFP.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\DAL.IFP.Interface.csproj", "projectName": "DAL.IFP.Interface", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\DAL.IFP.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj", "projectName": "DAL.IFP.www", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\DAL.IFP.www.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.www\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Device\\COM.IFP.Device.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Dictionary\\COM.IFP.Dictionary.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.PLC\\COM.IFP.PLC.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Cookie\\DAL.IFP.Cookie.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\DAL.IFP.Interface.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Interface\\DAL.IFP.Interface.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"NPOI": {"target": "Package", "version": "[2.7.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Quartz": {"target": "Package", "version": "[3.13.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\KY.IFP.Runtime.csproj": {"version": "2025.8.25.531", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\KY.IFP.Runtime.csproj", "projectName": "KY.IFP.Runtime", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\KY.IFP.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\KY.IFP.ServiceV2.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\KY.IFP.ServiceV2.csproj", "projectName": "KY.IFP.ServiceV2", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\KY.IFP.ServiceV2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.ServiceV2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\API.IFP.SignalR\\API.IFP.SignalR.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\KY.IFP.Runtime.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\KY.IFP.Runtime\\KY.IFP.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj", "projectName": "ORM.IFP.PLC", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\ORM.IFP.PLC.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.PLC\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj", "projectName": "ORM.IFP.www", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\ORM.IFP.www.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\ORM.IFP.www\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.SqlSugarN\\COM.IFP.SqlSugarN.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\SqlSugar.csproj": {"version": "5.1.4.151", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\SqlSugar.csproj", "projectName": "SqlSugar", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\SqlSugar.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\SqlSugar\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"DM.DmProvider": {"target": "Package", "version": "[8.3.1.30495, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.1, )"}, "MySqlConnector": {"target": "Package", "version": "[2.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "Npgsql": {"target": "Package", "version": "[5.0.7, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.100, )"}, "Oscar.Data.SqlClient": {"target": "Package", "version": "[4.0.4, )"}, "SqlSugarCore.Dm": {"target": "Package", "version": "[8.6.0, )"}, "SqlSugarCore.Kdbndp": {"target": "Package", "version": "[8.3.715, )"}, "System.Data.Common": {"target": "Package", "version": "[4.3.0, )"}, "System.Data.OleDb": {"target": "Package", "version": "[6.0.0, )"}, "System.Reflection.Emit.Lightweight": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}}}