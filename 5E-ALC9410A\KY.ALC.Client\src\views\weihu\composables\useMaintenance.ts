import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useGlobalSignalR } from '@/composables/useSignalR'
import { createControlMessage } from '@/utils/signalr-config'
import { ElMessage } from 'element-plus'
import type {
  MaintenanceConfig,
  DeviceStates,
  DeviceStatus,
  UseMaintenanceReturn,
  ParsedSignalRData,
  CommandResult
} from '../types/maintenance'

/**
 * 统一维护组件Hook
 * 处理所有维护组件的通用逻辑
 */
export function useMaintenance(config: MaintenanceConfig): UseMaintenanceReturn {
  const signalR = useGlobalSignalR()
  const isLoading = ref(false)
  const activeTab = ref('motor') // 默认激活第一个标签页

  // 基于配置自动生成初始状态
  const buttonStates = reactive<DeviceStates>(generateInitialStates(config))
  const deviceStatus = reactive<DeviceStatus>(generateInitialDeviceStatus(config))

  /**
   * 基于配置生成初始按钮状态
   */
  function generateInitialStates(config: MaintenanceConfig): DeviceStates {
    const states: DeviceStates = {}
    
    config.modules.forEach(module => {
      if (module.type === 'tabs' && module.tabs) {
        module.tabs.forEach(tab => {
          tab.modules.forEach(controlGroup => {
            if (!states[controlGroup.deviceType]) {
              states[controlGroup.deviceType] = {}
            }
            controlGroup.buttons.forEach(button => {
              states[controlGroup.deviceType][button.action] = false
            })
          })
        })
      } else if (module.deviceType && module.buttons) {
        // 直接控制组
        if (!states[module.deviceType]) {
          states[module.deviceType] = {}
        }
        module.buttons.forEach(button => {
          states[module.deviceType!][button.action] = false
        })
      }
    })
    
    return states
  }

  /**
   * 基于配置生成初始设备状态
   */
  function generateInitialDeviceStatus(config: MaintenanceConfig): DeviceStatus {
    const status: DeviceStatus = {}
    
    config.modules.forEach(module => {
      if (module.type === 'tabs' && module.tabs) {
        module.tabs.forEach(tab => {
          tab.modules.forEach(controlGroup => {
            if (controlGroup.statusIndicators) {
              if (!status[controlGroup.deviceType]) {
                status[controlGroup.deviceType] = {}
              }
              controlGroup.statusIndicators.forEach(indicator => {
                status[controlGroup.deviceType][indicator.key] = 'error'
              })
            }
          })
        })
      } else if (module.deviceType && module.statusIndicators) {
        // 直接控制组
        if (!status[module.deviceType]) {
          status[module.deviceType] = {}
        }
        module.statusIndicators.forEach(indicator => {
          status[module.deviceType!][indicator.key] = 'error'
        })
      }
    })
    
    return status
  }

  /**
   * 解析SignalR消息
   */
  function parseSignalRMessage(data: any): ParsedSignalRData {
    try {
      let parsedData = data
      if (typeof data === 'string') {
        parsedData = JSON.parse(data)
      }
      
      // 处理SignalR消息格式
      if (parsedData && parsedData.type && parsedData.arguments) {
        if (parsedData.arguments.length > 0) {
          const messageContent = parsedData.arguments[0]
          parsedData = typeof messageContent === 'string' ? JSON.parse(messageContent) : messageContent
        }
      }
      
      // 处理C字段格式
      if (parsedData && parsedData.C) {
        parsedData = typeof parsedData.C === 'string' ? JSON.parse(parsedData.C) : parsedData.C
      }
      
      return parsedData || {}
    } catch (error) {
      console.error(`[${config.name}] SignalR消息解析失败:`, error)
      return {}
    }
  }

  /**
   * 执行维护命令
   */
  async function executeMaintenanceCommand(command: string): Promise<CommandResult> {
    try {
      isLoading.value = true

      const message = createControlMessage('ExecuteMaintenanceCommand', { cmd: command })
      const response = await signalR.send('HandleCommand', JSON.stringify(message))

      // 解析后端返回的JSON响应
      let result: any = {}
      try {
        if (typeof response === 'string') {
          const parsedResponse = JSON.parse(response)
          if (parsedResponse.C) {
            result = JSON.parse(parsedResponse.C)
          }
        }
      } catch (error) {
        console.error(`[${config.name}] 响应解析失败:`, error)
        return { success: false, message: '响应解析失败' }
      }

      // 处理响应结果
      if (result?.Result === 1) {
        ElMessage.success("操作成功")
        console.log(`[${config.name}] 命令执行成功:`, command)
        return { success: true, data: result }
      } else {
        const errorMsg = result?.Msg || '未知错误'
        console.error(`[${config.name}] 命令执行失败:`, command, errorMsg)
        ElMessage.error(`操作失败: ${errorMsg}`)
        return { success: false, message: errorMsg }
      }
    } catch (error) {
      console.error(`[${config.name}] 命令发送异常:`, command, error)
      ElMessage.error('命令发送失败')
      return { success: false, message: '命令发送失败' }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 处理按钮点击
   */
  const handleButtonClick = async (deviceType: string, action: string): Promise<void> => {
    if (isLoading.value || !signalR.isConnected.value) {
      ElMessage.warning('系统未连接或正在处理中')
      return
    }

    const commandKey = `${deviceType}-${action}`
    const command = config.commandMap[commandKey]

    if (!command) {
      console.error(`[${config.name}] 未找到命令映射: ${commandKey}`)
      ElMessage.error(`未找到命令映射: ${commandKey}`)
      return
    }

    // 执行命令
    const result = await executeMaintenanceCommand(command)
    
    if (result.success) {
      // 更新按钮状态 - 实现互斥逻辑
      updateButtonStates(deviceType, action)
    }
  }

  /**
   * 更新按钮状态（互斥逻辑）
   */
  function updateButtonStates(deviceType: string, action: string) {
    if (buttonStates[deviceType]) {
      // 对于气缸类设备，实现伸出/缩回互斥
      if (deviceType.includes('气缸') || deviceType.includes('CYLINDER')) {
        Object.keys(buttonStates[deviceType]).forEach(key => {
          buttonStates[deviceType][key] = false
        })
        buttonStates[deviceType][action] = true
      }
      // 对于电机类设备，实现启动/停止互斥
      else if (deviceType.includes('电机') || deviceType.includes('ROLLER') || deviceType.includes('MOTOR')) {
        Object.keys(buttonStates[deviceType]).forEach(key => {
          buttonStates[deviceType][key] = false
        })
        buttonStates[deviceType][action] = true
      }
      // 其他设备的默认处理
      else {
        buttonStates[deviceType][action] = !buttonStates[deviceType][action]
      }
    }
  }

  /**
   * 处理实时数据更新
   */
  const handleRealtimeData = (data: any) => {
    try {
      const parsedData = parseSignalRMessage(data)
      
      if (!parsedData || typeof parsedData !== 'object') return
      
      // 使用配置中的处理器处理数据
      Object.entries(parsedData).forEach(([key, value]) => {
        const handler = config.realtimeHandlers[key]
        if (handler) {
          handler(Boolean(value), buttonStates, deviceStatus)
        }
      })
    } catch (error) {
      console.error(`[${config.name}] 实时数据处理异常:`, error)
    }
  }

  // 生命周期管理
  onMounted(() => {
    console.log(`[${config.name}] 维护组件已挂载`)
    
    const registerListener = () => {
      signalR.on('Message', handleRealtimeData)
    }

    if (signalR.isConnected.value) {
      registerListener()
    } else {
      const connectionCheckInterval = setInterval(() => {
        if (signalR.isConnected.value) {
          registerListener()
          clearInterval(connectionCheckInterval)
        }
      }, 1000)

      setTimeout(() => {
        clearInterval(connectionCheckInterval)
      }, 5000)
    }
  })

  onUnmounted(() => {
    console.log(`[${config.name}] 维护组件已卸载`)
    signalR.off('Message', handleRealtimeData)
  })

  return {
    buttonStates,
    deviceStatus,
    isLoading,
    activeTab,
    handleButtonClick
  }
}
