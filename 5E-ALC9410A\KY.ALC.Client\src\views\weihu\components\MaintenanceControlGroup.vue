<!--
  通用维护控制组组件
-->
<template>
  <div class="control-module" :class="moduleClass">
    <div class="module-title">{{ config.title }}</div>
    
    <!-- 按钮行 -->
    <div class="button-row">
      <button
        v-for="button in config.buttons"
        :key="button.action"
        :class="getButtonClass(button)"
        :disabled="isLoading"
        @click="handleButtonClick(button.action)"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        {{ button.label }}
      </button>
    </div>
    
    <!-- 状态指示器（如果有） -->
    <div v-if="config.statusIndicators && config.statusIndicators.length > 0" class="status-indicators">
      <div
        v-for="indicator in config.statusIndicators"
        :key="indicator.key"
        class="status-item"
      >
        <div :class="getStatusDotClass(indicator.key)"></div>
        <span class="status-text">{{ indicator.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ControlGroupConfig, ButtonConfig, DeviceStates, DeviceStatus } from '../types/maintenance'

interface Props {
  config: ControlGroupConfig
  buttonStates: DeviceStates
  deviceStatus: DeviceStatus
  isLoading: boolean
}

interface Emits {
  (e: 'button-click', deviceType: string, action: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算模块样式类
const moduleClass = computed(() => {
  const classes = []
  
  if (props.config.type === 'control-group-with-status') {
    classes.push('cylinder-module')
  } else {
    classes.push('motor-module')
  }
  
  return classes
})

// 获取按钮样式类
const getButtonClass = (button: ButtonConfig) => {
  const classes = ['control-btn']
  
  // 添加激活状态
  const isActive = props.buttonStates[props.config.deviceType]?.[button.action]
  if (isActive) {
    classes.push('active')
  }
  
  // 添加按钮样式
  if (button.style) {
    classes.push(`btn-${button.style}`)
  }
  
  return classes
}

// 获取状态指示器样式类
const getStatusDotClass = (statusKey: string) => {
  const classes = ['status-dot']
  
  const status = props.deviceStatus[props.config.deviceType]?.[statusKey]
  if (status) {
    classes.push(status)
  }
  
  return classes
}

// 处理按钮点击
const handleButtonClick = (action: string) => {
  emit('button-click', props.config.deviceType, action)
}
</script>

<style lang="scss" scoped>
.control-module {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  .module-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    text-align: center;
  }
  
  .button-row {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
    
    .control-btn {
      padding: 8px 16px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background: #fff;
      color: #495057;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      min-width: 60px;
      justify-content: center;
      
      &:hover:not(:disabled) {
        background: #e9ecef;
        border-color: #adb5bd;
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
      
      &.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      
      // 按钮样式变体
      &.btn-primary.active {
        background: #007bff;
        border-color: #007bff;
      }
      
      &.btn-success.active {
        background: #28a745;
        border-color: #28a745;
      }
      
      &.btn-danger.active {
        background: #dc3545;
        border-color: #dc3545;
      }
      
      &.btn-warning.active {
        background: #ffc107;
        border-color: #ffc107;
        color: #212529;
      }
      
      &.btn-info.active {
        background: #17a2b8;
        border-color: #17a2b8;
      }
      
      .loading-spinner {
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 6px;
      }
    }
  }
  
  .status-indicators {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 12px;
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        
        &.success {
          background: #28a745;
        }
        
        &.error {
          background: #dc3545;
        }
        
        &.warning {
          background: #ffc107;
        }
      }
      
      .status-text {
        font-size: 11px;
        color: #6c757d;
      }
    }
  }
}

// 模块类型样式
.motor-module {
  border-left: 4px solid #007bff;
}

.cylinder-module {
  border-left: 4px solid #28a745;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
