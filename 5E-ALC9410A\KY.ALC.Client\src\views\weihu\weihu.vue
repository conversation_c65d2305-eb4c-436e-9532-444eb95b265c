<!--
  统一维护面板组件
  通过路由参数决定加载哪个维护配置
-->
<template>
  <div v-if="config" class="maintenance-panel">
    <el-scrollbar class="page-scrollbar">
      <div class="page-container">
        <!-- 左侧图片区域 -->
        <div class="image-section">
          <div class="equipment-image">
            <img :src="config.imagePath" :alt="config.title" />
          </div>
        </div>
        
        <!-- 右侧控制区域 -->
        <div class="control-section">
          <MaintenanceControlGrid
            :modules="config.modules"
            :buttonStates="buttonStates"
            :deviceStatus="deviceStatus"
            :isLoading="isLoading"
            @button-click="handleButtonClick"
          />
        </div>
      </div>
    </el-scrollbar>
  </div>
  
  <!-- 配置加载失败 -->
  <div v-else class="error-container">
    <el-result
      icon="error"
      title="配置加载失败"
      :sub-title="`未找到维护模块配置: ${moduleType}`"
    >
      <template #extra>
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElScrollbar, ElResult, ElButton } from 'element-plus'
import { useMaintenance } from './composables/useMaintenance'
import { getMaintenanceConfig } from './configs'
import MaintenanceControlGrid from './components/MaintenanceControlGrid.vue'

defineOptions({
  name: "MaintenancePanel"
})

const route = useRoute()

// 从路由参数或props获取模块类型（支持动态路由）
const moduleType = computed(() => {
  // 优先使用props（适用于动态路由直接传参）
  if (route.meta?.module) {
    return route.meta.module as string
  }
  // 其次使用路由参数
  if (route.params.module) {
    return route.params.module as string
  }
  // 最后从路径中提取
  const pathMatch = route.path.match(/\/weihu(?:-new)?\/([^\/]+)/)
  return pathMatch ? pathMatch[1] : ''
})

// 加载对应的配置
const config = computed(() => {
  const module = moduleType.value
  if (!module) {
    console.error('未找到模块类型')
    return null
  }

  const moduleConfig = getMaintenanceConfig(module)
  if (!moduleConfig) {
    console.error(`未找到模块配置: ${module}`)
  }

  return moduleConfig
})

// 使用统一的维护逻辑
const {
  buttonStates,
  deviceStatus,
  isLoading,
  handleButtonClick
} = config.value ? useMaintenance(config.value) : {
  buttonStates: {},
  deviceStatus: {},
  isLoading: { value: false },
  handleButtonClick: () => {}
}

// 监听路由变化，重新加载配置
watch(() => route.params.module, (newModule) => {
  console.log('维护模块切换:', newModule)
}, { immediate: true })

// 调试：监听按钮状态变化
watch(() => JSON.stringify(buttonStates), (newVal) => {
  if (config.value) {
    console.log(`[${config.value.name}] 按钮状态变化:`, JSON.parse(newVal))
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.maintenance-panel {
  height: 100vh;
  
  .page-scrollbar {
    height: 100%;
    
    .page-container {
      display: flex;
      height: 100%;
      background: #f5f5f5;
      
      .image-section {
        flex: 0 0 40%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background: #fff;
        border-right: 1px solid #e9ecef;
        
        .equipment-image {
          max-width: 100%;
          max-height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
      
      .control-section {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #fff;
      }
      
      @media (max-width: 1024px) {
        flex-direction: column;
        
        .image-section {
          flex: 0 0 auto;
          max-height: 40vh;
          border-right: none;
          border-bottom: 1px solid #e9ecef;
        }
        
        .control-section {
          flex: 1;
        }
      }
    }
  }
}

.error-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}
</style>
