import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useGlobalSignalR } from '@/composables/useSignalR'
import { createControlMessage } from '@/utils/signalr-config'
import { ElMessage } from 'element-plus'

import type {
  ButtonStates,
  DeviceStatus,
  SystemStatus,
  RobotControl,
  GripperControl,
  UseGripperSignalRReturn
} from './types'

/**
 * 机械爪业务逻辑Hook
 * 按照框架标准结构组织代码
 */
export function useGripperSignalR(): UseGripperSignalRReturn {
  const signalR = useGlobalSignalR()

  // 加载状态
  const isLoading = ref(false)
  
  const commandMap: Record<string, string> = {
    // 机器人控制命令
    '机器人-启动': 'Manu_机器人启动',
    '机器人-停止': 'Manu_机器人停止',
    '机器人-复位': 'Manu_机器人复位',
    '机器人设置速度-设置': 'Manu_机器人设置速度',
    '机器人程序初始化-初始化': 'Manu_机器人程序初始化',
    '机器人状态机重置-重置': 'Manu_机器人状态机重置',
    '机器人任务读取-读取': 'Manu_机器人任务读取',

    // 夹爪控制命令
    '夹爪气缸-伸出': 'Manu_夹爪气缸伸出',
    '夹爪气缸-缩回': 'Manu_夹爪气缸缩回'
  }
  
  // 响应式状态 - 使用any类型避免严格类型检查
  const buttonStates = reactive<any>({
    '机器人': { '启动': false, '停止': false, '复位': false },
    '机器人设置速度': { '设置': false },
    '机器人程序初始化': { '初始化': false },
    '机器人状态机重置': { '重置': false },
    '机器人任务读取': { '读取': false },
    '夹爪气缸': { '伸出': false, '缩回': false }
  })
  
  // 设备状态指示器 - 初始状态为error（红色）
  const deviceStatus = reactive<DeviceStatus>({
    '夹爪气缸': {
      '伸出到位': 'error',
      '缩回到位': 'error'
    }
  })
  
  // 系统状态 - 简化为核心状态
  const systemStatus = reactive<SystemStatus>({
    connected: false
  })
  
  /**
   * 发送控制命令
   */
    const sendControlCommand = async (deviceType: string, action: string, params: any = {}): Promise<void> => {
    if (isLoading.value || !signalR.isConnected.value) {
      console.warn('SignalR未连接或正在处理其他命令')
      return
    }

    const commandKey = `${deviceType}-${action}`
    const backendCommand = commandMap[commandKey]

    if (!backendCommand) {
      console.error(`未找到命令映射: ${commandKey}`)
      return
    }

    try {
      isLoading.value = true

      // 维护命令统一调用ExecuteMaintenanceCommand，传入PLC常量作为cmd
      const message = createControlMessage('ExecuteMaintenanceCommand', { cmd: backendCommand })

      // 发送命令
      const response = await signalR.send('HandleCommand', JSON.stringify(message))

      // 解析后端返回的JSON响应
      let result: any = {}
      try {
        if (typeof response === 'string') {
          const parsedResponse = JSON.parse(response)
          if (parsedResponse.C) {
            result = JSON.parse(parsedResponse.C)
          }
        }
      } catch (error) {
        console.error('解析响应失败:', error)
        return
      }

      if (result?.Result === 1) {
        ElMessage.success("操作成功")
        // 更新按钮状态
        updateButtonState(deviceType, action)
        console.log(`机械手控制命令执行成功: ${deviceType} - ${action}`)
      } else {
        const errorMsg = result?.Msg || '未知错误'
        console.error(`命令执行失败: ${backendCommand}`, errorMsg)

        // 显示错误提示给用户
        ElMessage.error(`操作失败: ${errorMsg}`)
      }

    } catch (error) {
      console.error(`命令发送异常: ${backendCommand}`, error)
      ElMessage.error('命令发送失败')
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 更新按钮状态
   */
  const updateButtonState = (deviceType: string, action: string) => {
    if (buttonStates[deviceType]) {
      // 特殊处理机器人的启动和停止按钮互斥关系
      if (deviceType === '机器人') {
        if (action === '启动') {
          buttonStates[deviceType]['启动'] = true
          buttonStates[deviceType]['停止'] = false
          // 复位按钮保持原状态
        } else if (action === '停止') {
          buttonStates[deviceType]['停止'] = true
          buttonStates[deviceType]['启动'] = false
          // 复位按钮保持原状态
        } else if (action === '复位') {
          // 复位按钮切换状态，不影响启动/停止
          buttonStates[deviceType]['复位'] = true
          // 复位按钮点击后瞬时变色，然后等待后端状态更新
          setTimeout(() => {
            buttonStates[deviceType]['复位'] = false
          }, 500)
        }
      }
      // 处理夹爪气缸的伸出和缩回互斥
      else if (deviceType === '夹爪气缸') {
        if (action === '伸出') {
          buttonStates[deviceType]['伸出'] = true
          buttonStates[deviceType]['缩回'] = false
        } else if (action === '缩回') {
          buttonStates[deviceType]['缩回'] = true
          buttonStates[deviceType]['伸出'] = false
        }
      }
      // 其他设备按钮的常规处理
      else {
        // 切换当前按钮状态
        buttonStates[deviceType][action] = true
        
        // 其他按钮保持原状态，或根据需要重置
        Object.keys(buttonStates[deviceType]).forEach(key => {
          if (key !== action) {
            buttonStates[deviceType][key] = false
          }
        })
      }
    }
  }
  
  /**
   * 处理实时数据更新 - 简化版本
   */
  const handleRealtimeData = (data: any) => {
    try {
      // 解析SignalR消息
      let parsedData = data
      if (typeof data === 'string') {
        parsedData = JSON.parse(data)
      }

      // 处理SignalR消息格式
      if (parsedData && parsedData.type && parsedData.arguments) {
        if (parsedData.arguments.length > 0) {
          const messageContent = parsedData.arguments[0]
          parsedData = typeof messageContent === 'string' ? JSON.parse(messageContent) : messageContent
        }
      }

      // 处理C字段格式
      if (parsedData && parsedData.C) {
        parsedData = typeof parsedData.C === 'string' ? JSON.parse(parsedData.C) : parsedData.C
      }

      if (!parsedData || typeof parsedData !== 'object') return


      // 处理数据更新
      Object.entries(parsedData).forEach(([key, value]) => {
        const boolValue = Boolean(value)

        // 清理键名
        let cleanKey = key.replace(/\\u0022/g, '"').replace(/^"/, '').replace(/"$/, '')
        if (cleanKey.startsWith('S_')) cleanKey = cleanKey.substring(2)

        // 处理夹爪到位状态 - true显示绿色，false显示红色
        if (cleanKey.includes('夹爪气缸伸出到位') || key.includes('Manu_夹爪气缸伸出到位')) {
          const newStatus = boolValue ? 'success' : 'error'
          console.log('夹爪气缸伸出到位状态更新:', boolValue, '->', newStatus)
          deviceStatus['夹爪气缸']['伸出到位'] = newStatus
        }
        else if (cleanKey.includes('夹爪气缸缩回到位') || key.includes('Manu_夹爪气缸缩回到位')) {
          const newStatus = boolValue ? 'success' : 'error'
          console.log('夹爪气缸缩回到位状态更新:', boolValue, '->', newStatus)
          deviceStatus['夹爪气缸']['缩回到位'] = newStatus
        }

        // 处理机器人按钮状态
        else if (cleanKey.includes('机器人启动') || key.includes('Manu_机器人启动')) {
          buttonStates['机器人']['启动'] = boolValue
          if (boolValue) buttonStates['机器人']['停止'] = false
        }
        else if (cleanKey.includes('机器人停止') || key.includes('Manu_机器人停止')) {
          buttonStates['机器人']['停止'] = boolValue
          if (boolValue) buttonStates['机器人']['启动'] = false
        }
        else if (cleanKey.includes('机器人复位') || key.includes('Manu_机器人复位')) {
          buttonStates['机器人']['复位'] = boolValue
        }

        // 处理夹爪气缸按钮
        else if ((cleanKey.includes('夹爪气缸伸出') && !cleanKey.includes('到位')) ||
                 (key.includes('Manu_夹爪气缸伸出') && !key.includes('到位'))) {
          buttonStates['夹爪气缸']['伸出'] = boolValue
          if (boolValue) buttonStates['夹爪气缸']['缩回'] = false
        }
        else if ((cleanKey.includes('夹爪气缸缩回') && !cleanKey.includes('到位')) ||
                 (key.includes('Manu_夹爪气缸缩回') && !key.includes('到位'))) {
          buttonStates['夹爪气缸']['缩回'] = boolValue
          if (boolValue) buttonStates['夹爪气缸']['伸出'] = false
        }

        // 处理其他机器人功能按钮
        else if (cleanKey.includes('机器人程序初始化') || key.includes('Manu_机器人程序初始化')) {
          buttonStates['机器人程序初始化']['初始化'] = boolValue
        }
        else if (cleanKey.includes('机器人状态机重置') || key.includes('Manu_机器人状态机重置')) {
          buttonStates['机器人状态机重置']['重置'] = boolValue
        }
        else if (cleanKey.includes('机器人任务读取') || key.includes('Manu_机器人任务读取')) {
          buttonStates['机器人任务读取']['读取'] = boolValue
        }
        else if (cleanKey.includes('机器人设置速度') || key.includes('Manu_机器人设置速度')) {
          buttonStates['机器人设置速度']['设置'] = boolValue
        }
      })
    } catch (error) {
      console.error('机械爪处理实时数据异常:', error)
    }
  }
  
  /**
   * 机器人控制方法
   */
  const robotControl: RobotControl = {
    start: (params = {}) => sendControlCommand('机器人', '启动', params),
    stop: (params = {}) => sendControlCommand('机器人', '停止', params),
    reset: (params = {}) => sendControlCommand('机器人', '复位', params),
    setSpeed: (speed: number) => sendControlCommand('机器人设置速度', '设置', { speed }),
    initProgram: (params = {}) => sendControlCommand('机器人程序初始化', '初始化', params),
    resetState: (params = {}) => sendControlCommand('机器人状态机重置', '重置', params),
    readTask: (params = {}) => sendControlCommand('机器人任务读取', '读取', params)
  }
  
  /**
   * 夹爪控制方法
   */
  const gripperControl: GripperControl = {
    extend: (params = {}) => sendControlCommand('夹爪气缸', '伸出', params),
    retract: (params = {}) => sendControlCommand('夹爪气缸', '缩回', params)
  }
  
  /**
   * 通用按钮控制方法（兼容原有接口）
   */
  const toggleSingleButton = async (deviceType: string, action: string): Promise<void> => {
    await sendControlCommand(deviceType, action)
  }

  /**
   * 获取系统状态
   */
  const getSystemStatus = (): SystemStatus => {
    return {
      connected: signalR.isConnected.value
    }
  }
  
  /**
   * 连接状态检查
   */
  const checkConnection = (): boolean => signalR.isConnected.value


  
  // 组件挂载时设置监听
  onMounted(() => {
    // 注册监听器
    const registerListener = () => {
      signalR.on('Message', handleRealtimeData)
    }

    // 如果已经连接，直接注册监听器
    if (signalR.isConnected.value) {
      registerListener()
    } else {
      // 定期检查连接状态
      const connectionCheckInterval = setInterval(() => {
        if (signalR.isConnected.value) {
          registerListener()
          clearInterval(connectionCheckInterval)
        }
      }, 1000)

      // 超时后停止检查
      setTimeout(() => {
        clearInterval(connectionCheckInterval)
      }, 5000)
    }
  })

  // 组件卸载时清理监听
  onUnmounted(() => {
    signalR.off('Message', handleRealtimeData)
  })
  
  return {
    // 状态
    buttonStates,
    deviceStatus,
    systemStatus,
    isLoading,

    // 控制方法
    robotControl,
    gripperControl,
    toggleSingleButton,

    // 工具方法
    sendControlCommand,
    getSystemStatus,
    checkConnection
  }
}
