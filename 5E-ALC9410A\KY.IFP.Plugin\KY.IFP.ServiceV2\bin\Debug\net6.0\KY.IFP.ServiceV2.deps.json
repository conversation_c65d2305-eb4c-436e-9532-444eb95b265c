{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"KY.IFP.ServiceV2/1.0.0": {"dependencies": {"API.IFP.SignalR": "1.0.0", "COM.IFP.Client": "1.0.0", "COM.IFP.Common": "1.0.0", "COM.IFP.Log": "1.0.0", "KY.IFP.Runtime": "2025.8.25.531"}, "runtime": {"KY.IFP.ServiceV2.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1", "Microsoft.Extensions.Primitives": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}}, "Microsoft.Extensions.Options/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Primitives/6.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "System.Configuration.ConfigurationManager/6.0.2": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.1"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "System.Diagnostics.PerformanceCounter/6.0.2": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.2"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.1": {"dependencies": {"System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "API.IFP.SignalR/1.0.0": {"runtime": {"API.IFP.SignalR.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "COM.IFP.Client/1.0.0": {"dependencies": {"COM.IFP.Common": "1.0.0", "COM.IFP.ComputerInfo": "1.0.0", "COM.IFP.Log": "1.0.0", "COM.IFP.UDPHelper": "1.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"COM.IFP.Client.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "COM.IFP.Common/1.0.0": {"dependencies": {"COM.IFP.Log": "1.0.0", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "6.0.0"}, "runtime": {"COM.IFP.Common.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "COM.IFP.ComputerInfo/1.0.0": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.2"}, "runtime": {"COM.IFP.ComputerInfo.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "COM.IFP.Log/1.0.0": {"runtime": {"COM.IFP.Log.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "COM.IFP.UDPHelper/1.0.0": {"runtime": {"COM.IFP.UDPHelper.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "KY.IFP.Runtime/2025.8.25.531": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "6.0.3"}, "runtime": {"KY.IFP.Runtime.dll": {"assemblyVersion": "2025.8.25.531", "fileVersion": "2025.8.22.1194"}}}}}, "libraries": {"KY.IFP.ServiceV2/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zEtKmOJ3sZ9YxRgvgLtoQsqO069Kqp0aC6Ai+DkyE5uahtu1ynvuoDVFQgyyhVcJWnxCzZHax/3AodvAx2mhjA==", "path": "microsoft.extensions.caching.abstractions/6.0.1", "hashPath": "microsoft.extensions.caching.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-GVNNcHoPDEUn4OQiBBGs5mE6nX7BA+LeQId9NeA+gB8xcbDUmFPAl8Er2ixNLbn4ffFr8t5jfMwdxgFG66k7BA==", "path": "microsoft.extensions.caching.memory/6.0.3", "hashPath": "microsoft.extensions.caching.memory.6.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-K14wYgwOfKVELrUh5eBqlC8Wvo9vvhS3ZhIvcswV2uS/ubkTRPSQsN557EZiYUSSoZNxizG+alN4wjtdyLdcyw==", "path": "microsoft.extensions.logging.abstractions/6.0.4", "hashPath": "microsoft.extensions.logging.abstractions.6.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v5rh5jRcLBOKOaLVyYCm4TY/RoJlxWsW7N2TAPkmlHe55/0cB0Syp979x4He1+MIXsaTvJl1WOc7b1D1PSsO3A==", "path": "microsoft.extensions.options/6.0.1", "hashPath": "microsoft.extensions.options.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zyJfttJduuQbGEsd/TgerUEdgzHgUn/6oFgeaE04DKUMs7bbzckV7Ci1QZxf/VyQAlG41gR/GjecEScuYoHCUg==", "path": "microsoft.extensions.primitives/6.0.1", "hashPath": "microsoft.extensions.primitives.6.0.1.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-LtqgY79MRntWIM0AeQf4uj1mGyqpVhR7O9N1+UODQu/EGQwRTERqMqpYTeedE7VTK2ngoCtxzAaTn9gRDa+6Qw==", "path": "system.configuration.configurationmanager/6.0.2", "hashPath": "system.configuration.configurationmanager.6.0.2.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4SLKj8/YJ+uA7YxmKmTfk6Pnn89nxIDipfNaApe1E3I2SI+f1+INj75ysTAtKwIWj7yQK8iiUjlRcJfiINwFUQ==", "path": "system.diagnostics.performancecounter/6.0.2", "hashPath": "system.diagnostics.performancecounter.6.0.2.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QufGNXopGXxdXbkDn87hta4ajdNKNI3a1+HoJbKkmBbMtYPhEgEJKSiPZHGjI0zQpgZ7gi5L0gSV3PeewKRtew==", "path": "system.security.permissions/6.0.1", "hashPath": "system.security.permissions.6.0.1.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "API.IFP.SignalR/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "COM.IFP.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "COM.IFP.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "COM.IFP.ComputerInfo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "COM.IFP.Log/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "COM.IFP.UDPHelper/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "KY.IFP.Runtime/2025.8.25.531": {"type": "project", "serviceable": false, "sha512": ""}}}