import type { MaintenanceConfig } from '../types/maintenance'

/**
 * 内桶板链维护配置
 */
export const neitongbanlianConfig: MaintenanceConfig = {
  name: '内桶板链',
  title: '内桶板链设备图',
  imagePath: '/src/assets/images/neitongbanlian.png',

  modules: [
    {
      type: 'tabs',
      tabs: [
        {
          name: 'motor',
          label: '滚筒电机',
          modules: [
            {
              type: 'control-group',
              title: '快检重桶出桶滚筒电机',
              deviceType: 'QUICK_INSPECT_ROLLER',
              buttons: [
                { action: 'START', label: '启动', style: 'primary' },
                { action: 'STOP', label: '停止', style: 'danger' }
              ]
            },
            {
              type: 'control-group',
              title: '制样重桶出桶滚筒电机',
              deviceType: 'SAMPLE_PREP_ROLLER',
              buttons: [
                { action: 'START', label: '启动', style: 'primary' },
                { action: 'STOP', label: '停止', style: 'danger' }
              ]
            },
            {
              type: 'control-group',
              title: '存样重桶出桶滚筒电机',
              deviceType: 'SAMPLE_STORE_ROLLER',
              buttons: [
                { action: 'START', label: '启动', style: 'primary' },
                { action: 'STOP', label: '停止', style: 'danger' }
              ]
            },
            {
              type: 'control-group',
              title: '外部异样桶出桶滚筒电机',
              deviceType: 'EXTERNAL_ROLLER',
              buttons: [
                { action: 'FORWARD', label: '正转', style: 'success' },
                { action: 'REVERSE', label: '反转', style: 'warning' }
              ]
            }
          ]
        },
        {
          name: 'cylinder',
          label: '挡桶气缸',
          modules: [
            {
              type: 'control-group-with-status',
              title: '快检重桶出桶挡桶气缸',
              deviceType: 'QUICK_INSPECT_CYLINDER',
              buttons: [
                { action: 'EXTEND', label: '伸出', style: 'primary' },
                { action: 'RETRACT', label: '缩回', style: 'info' }
              ],
              statusIndicators: [
                { key: '伸出到位', label: '伸出到位' },
                { key: '缩回到位', label: '缩回到位' }
              ]
            },
            {
              type: 'control-group-with-status',
              title: '制样重桶出桶挡桶气缸',
              deviceType: 'SAMPLE_PREP_CYLINDER',
              buttons: [
                { action: 'EXTEND', label: '伸出', style: 'primary' },
                { action: 'RETRACT', label: '缩回', style: 'info' }
              ],
              statusIndicators: [
                { key: '伸出到位', label: '伸出到位' },
                { key: '缩回到位', label: '缩回到位' }
              ]
            },
            {
              type: 'control-group-with-status',
              title: '存样重桶出桶挡桶气缸',
              deviceType: 'SAMPLE_STORE_CYLINDER',
              buttons: [
                { action: 'EXTEND', label: '伸出', style: 'primary' },
                { action: 'RETRACT', label: '缩回', style: 'info' }
              ],
              statusIndicators: [
                { key: '伸出到位', label: '伸出到位' },
                { key: '缩回到位', label: '缩回到位' }
              ]
            }
          ]
        }
      ]
    }
  ],

  // 命令映射
  commandMap: {
    // 滚筒电机控制
    'QUICK_INSPECT_ROLLER-START': 'Manu_快检重桶出桶滚筒电机1',
    'QUICK_INSPECT_ROLLER-STOP': 'Manu_快检重桶出桶滚筒电机2',
    'SAMPLE_PREP_ROLLER-START': 'Manu_制样重桶出桶滚筒电机1',
    'SAMPLE_PREP_ROLLER-STOP': 'Manu_制样重桶出桶滚筒电机2',
    'SAMPLE_STORE_ROLLER-START': 'Manu_存样重桶出桶滚筒电机1',
    'SAMPLE_STORE_ROLLER-STOP': 'Manu_存样重桶出桶滚筒电机2',
    'EXTERNAL_ROLLER-FORWARD': 'Manu_外部异样桶出桶滚筒电机正转',
    'EXTERNAL_ROLLER-REVERSE': 'Manu_外部异样桶出桶滚筒电机反转',

    // 挡桶气缸控制
    'QUICK_INSPECT_CYLINDER-EXTEND': 'Manu_快检重桶出桶挡桶气缸1伸出',
    'QUICK_INSPECT_CYLINDER-RETRACT': 'Manu_快检重桶出桶挡桶气缸1缩回',
    'SAMPLE_PREP_CYLINDER-EXTEND': 'Manu_制样重桶出桶挡桶气缸1伸出',
    'SAMPLE_PREP_CYLINDER-RETRACT': 'Manu_制样重桶出桶挡桶气缸1缩回',
    'SAMPLE_STORE_CYLINDER-EXTEND': 'Manu_存样重桶出桶挡桶气缸1伸出',
    'SAMPLE_STORE_CYLINDER-RETRACT': 'Manu_存样重桶出桶挡桶气缸1缩回'
  },

  // 实时数据处理器
  realtimeHandlers: {
    // 滚筒电机状态处理
    'Manu_快检重桶出桶滚筒电机1': (value, buttonStates) => {
      buttonStates.QUICK_INSPECT_ROLLER.START = value
      if (value) buttonStates.QUICK_INSPECT_ROLLER.STOP = false
    },
    'Manu_快检重桶出桶滚筒电机2': (value, buttonStates) => {
      buttonStates.QUICK_INSPECT_ROLLER.STOP = value
      if (value) buttonStates.QUICK_INSPECT_ROLLER.START = false
    },
    'Manu_制样重桶出桶滚筒电机1': (value, buttonStates) => {
      buttonStates.SAMPLE_PREP_ROLLER.START = value
      if (value) buttonStates.SAMPLE_PREP_ROLLER.STOP = false
    },
    'Manu_制样重桶出桶滚筒电机2': (value, buttonStates) => {
      buttonStates.SAMPLE_PREP_ROLLER.STOP = value
      if (value) buttonStates.SAMPLE_PREP_ROLLER.START = false
    },
    'Manu_存样重桶出桶滚筒电机1': (value, buttonStates) => {
      buttonStates.SAMPLE_STORE_ROLLER.START = value
      if (value) buttonStates.SAMPLE_STORE_ROLLER.STOP = false
    },
    'Manu_存样重桶出桶滚筒电机2': (value, buttonStates) => {
      buttonStates.SAMPLE_STORE_ROLLER.STOP = value
      if (value) buttonStates.SAMPLE_STORE_ROLLER.START = false
    },
    'Manu_外部异样桶出桶滚筒电机正转': (value, buttonStates) => {
      buttonStates.EXTERNAL_ROLLER.FORWARD = value
      if (value) buttonStates.EXTERNAL_ROLLER.REVERSE = false
    },
    'Manu_外部异样桶出桶滚筒电机反转': (value, buttonStates) => {
      buttonStates.EXTERNAL_ROLLER.REVERSE = value
      if (value) buttonStates.EXTERNAL_ROLLER.FORWARD = false
    },

    // 气缸状态处理
    'Manu_快检重桶出桶挡桶气缸1伸出': (value, buttonStates) => {
      buttonStates.QUICK_INSPECT_CYLINDER.EXTEND = value
      if (value) buttonStates.QUICK_INSPECT_CYLINDER.RETRACT = false
    },
    'Manu_快检重桶出桶挡桶气缸1缩回': (value, buttonStates) => {
      buttonStates.QUICK_INSPECT_CYLINDER.RETRACT = value
      if (value) buttonStates.QUICK_INSPECT_CYLINDER.EXTEND = false
    },
    'Manu_制样重桶出桶挡桶气缸1伸出': (value, buttonStates) => {
      buttonStates.SAMPLE_PREP_CYLINDER.EXTEND = value
      if (value) buttonStates.SAMPLE_PREP_CYLINDER.RETRACT = false
    },
    'Manu_制样重桶出桶挡桶气缸1缩回': (value, buttonStates) => {
      buttonStates.SAMPLE_PREP_CYLINDER.RETRACT = value
      if (value) buttonStates.SAMPLE_PREP_CYLINDER.EXTEND = false
    },
    'Manu_存样重桶出桶挡桶气缸1伸出': (value, buttonStates) => {
      buttonStates.SAMPLE_STORE_CYLINDER.EXTEND = value
      if (value) buttonStates.SAMPLE_STORE_CYLINDER.RETRACT = false
    },
    'Manu_存样重桶出桶挡桶气缸1缩回': (value, buttonStates) => {
      buttonStates.SAMPLE_STORE_CYLINDER.RETRACT = value
      if (value) buttonStates.SAMPLE_STORE_CYLINDER.EXTEND = false
    },

    // 气缸到位状态处理
    'S_快检重桶出桶挡桶气缸1伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.QUICK_INSPECT_CYLINDER) {
        deviceStatus.QUICK_INSPECT_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_快检重桶出桶挡桶气缸1缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.QUICK_INSPECT_CYLINDER) {
        deviceStatus.QUICK_INSPECT_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    },
    'S_制样重桶出桶挡桶气缸1伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.SAMPLE_PREP_CYLINDER) {
        deviceStatus.SAMPLE_PREP_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_制样重桶出桶挡桶气缸1缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.SAMPLE_PREP_CYLINDER) {
        deviceStatus.SAMPLE_PREP_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    },
    'S_存样重桶出桶挡桶气缸1伸出到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.SAMPLE_STORE_CYLINDER) {
        deviceStatus.SAMPLE_STORE_CYLINDER['伸出到位'] = value ? 'success' : 'error'
      }
    },
    'S_存样重桶出桶挡桶气缸1缩回到位': (value, buttonStates, deviceStatus) => {
      if (deviceStatus.SAMPLE_STORE_CYLINDER) {
        deviceStatus.SAMPLE_STORE_CYLINDER['缩回到位'] = value ? 'success' : 'error'
      }
    }
  }
}
