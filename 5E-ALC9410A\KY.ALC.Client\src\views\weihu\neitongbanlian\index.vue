<!--
  @Description: 内桶板链
-->
<script setup lang="ts">
import { useNeitongbanlianSignalR } from './utils/hook'
import { DeviceType, ActionType } from './utils/types'

defineOptions({
  name: "WeihuNeitongbanlian"
})

// 使用内桶板链控制Hook
const {
  buttonStates,
  deviceStatus,
  activeTab,
  toggleSingleButton,
  isLoading
} = useNeitongbanlianSignalR()

// 按钮点击处理
const handleButtonClick = async (deviceType: string, action: string) => {
  try {
    await toggleSingleButton(deviceType, action)
  } catch (error) {
    console.error('按钮操作失败:', error)
  }
}

// 调试：监听按钮状态变化 - 修复代理对象问题
import { watch } from 'vue'
watch(() => JSON.stringify(buttonStates), (newVal) => {
  console.log('内桶板链按钮状态变化:', JSON.parse(newVal))
}, { immediate: true })
</script>

<template>
  <el-scrollbar class="page-scrollbar">
    <div class="page-container">
      <!-- 左侧图片区域 -->
      <div class="image-section">
        <div class="equipment-image">
          <img src="/src/assets/images/neitongbanlian.png" alt="内桶板链设备图" />
        </div>
      </div>
      
      <!-- 右侧控制按钮区域 -->
      <div class="control-section">
        <!-- 标签页头部 -->
        <div class="tab-header">
          <button 
            :class="['tab-btn', { active: activeTab === 'motor' }]"
            @click="activeTab = 'motor'"
          >
            滚筒电机
          </button>
          <button 
            :class="['tab-btn', { active: activeTab === 'cylinder' }]"
            @click="activeTab = 'cylinder'"
          >
            挡桶气缸
          </button>
        </div>
        
        <!-- 标签页内容区域 -->
        <div class="tab-content">
          <!-- 滚筒电机标签页 -->
          <div v-if="activeTab === 'motor'" class="tab-panel">
            <div class="control-grid">
              <!-- 快检重桶出桶滚筒电机 -->
              <div class="control-module motor-module">
                <div class="module-title">{{ DeviceType.QUICK_INSPECT_ROLLER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.QUICK_INSPECT_ROLLER]?.[ActionType.START] }]"
                    @click="handleButtonClick(DeviceType.QUICK_INSPECT_ROLLER, ActionType.START)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.START }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.QUICK_INSPECT_ROLLER]?.[ActionType.STOP] }]"
                    @click="handleButtonClick(DeviceType.QUICK_INSPECT_ROLLER, ActionType.STOP)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.STOP }}
                  </button>
                </div>
              </div>

              <!-- 制样重桶出桶滚筒电机 -->
              <div class="control-module motor-module">
                <div class="module-title">{{ DeviceType.SAMPLE_PREP_ROLLER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_PREP_ROLLER]?.[ActionType.START] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_PREP_ROLLER, ActionType.START)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.START }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_PREP_ROLLER]?.[ActionType.STOP] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_PREP_ROLLER, ActionType.STOP)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.STOP }}
                  </button>
                </div>
              </div>

              <!-- 存样重桶出桶滚筒电机 -->
              <div class="control-module motor-module">
                <div class="module-title">{{ DeviceType.SAMPLE_STORE_ROLLER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_STORE_ROLLER]?.[ActionType.START] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_STORE_ROLLER, ActionType.START)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.START }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_STORE_ROLLER]?.[ActionType.STOP] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_STORE_ROLLER, ActionType.STOP)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.STOP }}
                  </button>
                </div>
              </div>

              <!-- 外部异样桶出桶滚筒电机 -->
              <div class="control-module motor-module">
                <div class="module-title">{{ DeviceType.EXTERNAL_ROLLER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.EXTERNAL_ROLLER]?.[ActionType.FORWARD] }]"
                    @click="handleButtonClick(DeviceType.EXTERNAL_ROLLER, ActionType.FORWARD)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.FORWARD }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.EXTERNAL_ROLLER]?.[ActionType.REVERSE] }]"
                    @click="handleButtonClick(DeviceType.EXTERNAL_ROLLER, ActionType.REVERSE)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.REVERSE }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 挡桶气缸标签页 -->
          <div v-if="activeTab === 'cylinder'" class="tab-panel">
            <div class="control-grid">
              <!-- 快检重桶出桶挡桶气缸 -->
              <div class="control-module cylinder-module">
                <div class="module-title">{{ DeviceType.QUICK_INSPECT_CYLINDER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.QUICK_INSPECT_CYLINDER]?.[ActionType.EXTEND] }]"
                    @click="handleButtonClick(DeviceType.QUICK_INSPECT_CYLINDER, ActionType.EXTEND)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.EXTEND }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.QUICK_INSPECT_CYLINDER]?.[ActionType.RETRACT] }]"
                    @click="handleButtonClick(DeviceType.QUICK_INSPECT_CYLINDER, ActionType.RETRACT)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.RETRACT }}
                  </button>
                </div>
                <div class="status-indicators">
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.QUICK_INSPECT_CYLINDER]?.['伸出到位']]"></div>
                    <span class="status-text">伸出到位</span>
                  </div>
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.QUICK_INSPECT_CYLINDER]?.['缩回到位']]"></div>
                    <span class="status-text">缩回到位</span>
                  </div>
                </div>
              </div>

              <!-- 制样重桶出桶挡桶气缸 -->
              <div class="control-module cylinder-module">
                <div class="module-title">{{ DeviceType.SAMPLE_PREP_CYLINDER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_PREP_CYLINDER]?.[ActionType.EXTEND] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_PREP_CYLINDER, ActionType.EXTEND)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.EXTEND }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_PREP_CYLINDER]?.[ActionType.RETRACT] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_PREP_CYLINDER, ActionType.RETRACT)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.RETRACT }}
                  </button>
                </div>
                <div class="status-indicators">
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.SAMPLE_PREP_CYLINDER]?.['伸出到位']]"></div>
                    <span class="status-text">伸出到位</span>
                  </div>
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.SAMPLE_PREP_CYLINDER]?.['缩回到位']]"></div>
                    <span class="status-text">缩回到位</span>
                  </div>
                </div>
              </div>

              <!-- 存样重桶出桶挡桶气缸 -->
              <div class="control-module cylinder-module">
                <div class="module-title">{{ DeviceType.SAMPLE_STORE_CYLINDER }}</div>
                <div class="button-row">
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_STORE_CYLINDER]?.[ActionType.EXTEND] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_STORE_CYLINDER, ActionType.EXTEND)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.EXTEND }}
                  </button>
                  <button 
                    :class="['control-btn', { active: buttonStates[DeviceType.SAMPLE_STORE_CYLINDER]?.[ActionType.RETRACT] }]"
                    @click="handleButtonClick(DeviceType.SAMPLE_STORE_CYLINDER, ActionType.RETRACT)"
                    :disabled="isLoading"
                  >
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ ActionType.RETRACT }}
                  </button>
                </div>
                <div class="status-indicators">
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.SAMPLE_STORE_CYLINDER]?.['伸出到位']]"></div>
                    <span class="status-text">伸出到位</span>
                  </div>
                  <div class="status-item">
                    <div :class="['status-dot', deviceStatus[DeviceType.SAMPLE_STORE_CYLINDER]?.['缩回到位']]"></div>
                    <span class="status-text">缩回到位</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<style lang="scss" scoped src="./index.scss"></style>
